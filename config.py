# config.py
import os
from enum import Enum
from typing import Dict, Any, List, Optional, Union, Literal, TypedDict, overload
from dotenv import load_dotenv # 用于从 .env 文件加载环境变量 (如API密钥)
import pandas as pd           # 用于时间处理，例如 pd.Timedelta

# --- 枚举和常量定义 ---
class TargetType(Enum):
    """目标变量类型枚举"""
    UP_ONLY = "UP_ONLY"
    DOWN_ONLY = "DOWN_ONLY"
    BOTH = "BOTH"
    META_MODEL_DISPLAY = "META_MODEL_DISPLAY"

class DeviceType(Enum):
    """设备类型枚举"""
    CPU = "cpu"
    GPU = "gpu"

class TriggerType(Enum):
    """预测触发类型枚举"""
    KLINE_CLOSE = "kline_close"
    APSCHEDULER_DRIVEN = "apscheduler_driven"

class TrendIndicatorType(Enum):
    """趋势指标类型枚举"""
    ADX = "adx"
    EMA_CROSS = "ema_cross"

class OptimizationMethod(Enum):
    """阈值优化方法枚举"""
    F1 = "f1"
    PRECISION_RECALL = "precision_recall"
    YOUDEN = "youden"
    BALANCED = "balanced"
    PRECISION_CONSTRAINED_RECALL = "precision_constrained_recall"

# 基础模型名称常量
class BaseModelNames:
    """基础模型名称常量"""
    BTC_15M_UP = "BTC_15m_UP"
    BTC_15M_DOWN = "BTC_15m_DOWN"
    META_SIGNAL_BTC = "MetaSignal_BTC"

# 概率类型常量
class ProbabilityTypes:
    """概率类型常量"""
    P_FAVORABLE = "p_favorable"
    P_UP = "p_up"
    P_DOWN = "p_down"

# --- 类型定义 ---
# 设备类型字面量
DeviceTypeLiteral = Literal["cpu", "gpu"]

# 目标变量类型字面量
TargetVariableTypeLiteral = Literal["UP_ONLY", "DOWN_ONLY", "BOTH", "META_MODEL_DISPLAY"]

# 触发类型字面量
TriggerTypeLiteral = Literal["kline_close", "apscheduler_driven"]

# 趋势指标类型字面量
TrendIndicatorTypeLiteral = Literal["adx", "ema_cross"]

# 优化方法字面量
OptimizationMethodLiteral = Literal["f1", "precision_recall", "youden", "balanced", "precision_constrained_recall"]

# 缩放器类型字面量
ScalerTypeLiteral = Literal["minmax", "standard"]

# 概率类型字面量
ProbabilityTypeLiteral = Literal["p_favorable", "p_up", "p_down"]

# LightGBM参数类型定义
class LightGBMParams(TypedDict, total=False):
    """LightGBM参数类型定义"""
    objective: str
    metric: str
    class_weight: Optional[Dict[int, float]]
    boosting_type: str
    random_state: int
    n_estimators: int
    early_stopping_rounds: int
    verbose: int
    verbosity: int
    learning_rate: float
    num_leaves: int
    max_depth: int
    reg_alpha: float
    reg_lambda: float
    colsample_bytree: float
    subsample: float
    min_child_samples: int
    subsample_freq: int

# 特征工程参数类型定义
class FeatureEngineeringParams(TypedDict, total=False):
    """特征工程参数类型定义"""
    enable_price_change: bool
    enable_volume: bool
    enable_candle: bool
    enable_ta: bool
    enable_time: bool
    enable_fund_flow: bool
    enable_mtfa: bool
    enable_pattern_recognition: bool
    enable_trend_slope: bool
    price_change_periods: List[int]
    volume_avg_period: int
    atr_period: int
    rsi_period: int
    macd_fast: int
    macd_slow: int
    macd_sign: int
    stoch_k: int
    stoch_d: int
    stoch_smooth_k: int

# SMOTE参数类型定义
class SMOTEParams(TypedDict, total=False):
    """SMOTE参数类型定义"""
    smote_enable: bool
    smote_k_neighbors: int
    smote_min_samples_threshold: int
    smote_random_state: int

# 阈值优化参数类型定义
class ThresholdOptimizationParams(TypedDict, total=False):
    """阈值优化参数类型定义"""
    threshold_optimization_enable: bool
    threshold_optimization_method: OptimizationMethodLiteral
    threshold_save_to_metadata: bool
    threshold_default_value: float
    threshold_use_independent_validation: bool
    threshold_independent_val_ratio: float
    threshold_min_precision: float
    threshold_precision_constraint_fallback: bool

# APScheduler配置类型定义
class APSchedulerConfig(TypedDict, total=False):
    """APScheduler配置类型定义"""
    apscheduler_job_enabled: bool
    apscheduler_trigger_type: Literal["cron", "interval", "date"]
    apscheduler_cron_config: Dict[str, Union[str, int]]

# 完整的目标配置类型定义
class TargetConfig(TypedDict, total=False):
    """完整的目标配置类型定义"""
    # 基本信息
    name: str
    symbol: str
    scaler_type: ScalerTypeLiteral
    device_type: DeviceTypeLiteral
    interval: str
    interval_timedelta: Optional[pd.Timedelta]
    prediction_periods: List[int]
    prediction_minutes_display: Union[int, str]
    model_save_dir: str
    target_variable_type: TargetVariableTypeLiteral
    drop_neutral_targets: bool
    prediction_trigger_type: TriggerTypeLiteral
    target_threshold: float
    signal_threshold: float

    # 数据处理参数
    data_fetch_limit: int
    train_ratio: float
    validation_ratio: float

    # 特征工程参数（继承自FeatureEngineeringParams）
    enable_price_change: bool
    enable_volume: bool
    enable_candle: bool
    enable_ta: bool
    enable_time: bool
    enable_fund_flow: bool
    enable_mtfa: bool
    enable_pattern_recognition: bool
    enable_trend_slope: bool

    # LightGBM参数（继承自LightGBMParams）
    objective: str
    metric: str
    class_weight: Optional[Dict[int, float]]
    boosting_type: str
    random_state: int
    n_estimators: int
    early_stopping_rounds: int
    learning_rate: float
    num_leaves: int
    max_depth: int
    reg_alpha: float
    reg_lambda: float
    colsample_bytree: float
    subsample: float
    min_child_samples: int

    # SMOTE参数（继承自SMOTEParams）
    smote_enable: bool
    smote_k_neighbors: int
    smote_min_samples_threshold: int
    smote_random_state: int

    # 阈值优化参数（继承自ThresholdOptimizationParams）
    threshold_optimization_enable: bool
    threshold_optimization_method: OptimizationMethodLiteral
    threshold_save_to_metadata: bool
    threshold_default_value: float
    threshold_use_independent_validation: bool
    threshold_independent_val_ratio: float

    # APScheduler参数（继承自APSchedulerConfig）
    apscheduler_job_enabled: bool
    apscheduler_trigger_type: Literal["cron", "interval", "date"]
    apscheduler_cron_config: Dict[str, Union[str, int]]

# 类型别名
ConfigDict = Dict[str, Any]  # 通用配置字典类型
TargetConfigDict = TargetConfig  # 目标配置字典类型

# 加载 .env 文件中的环境变量 (如果存在的话)
# 这允许你将敏感信息（如API密钥）存储在 .env 文件中，而不是直接写入代码
load_dotenv()

# --- Binance API 密钥 ---
# 用于与Binance API进行认证的通信，例如获取历史数据、账户信息等。
# 建议将这些密钥存储在 .env 文件中，以增强安全性。
API_KEY = os.getenv("BINANCE_API_KEY", None)        # Binance API Key
API_SECRET = os.getenv("BINANCE_API_SECRET", None)  # Binance API Secret

# --- APScheduler 时区配置 ---
# APScheduler (用于定时任务，如按计划触发预测) 使用的时区。
# 推荐与应用程序的主要时区 (APP_TIMEZONE 在 main.py 中定义) 保持一致，以避免混淆。
APScheduler_TIMEZONE = "Asia/Shanghai" # 例如 "UTC", "America/New_York"

# --- 全局通用设置 ---
SYMBOL = 'BTCUSDT'              # [字符串] 默认交易对，当单个预测目标配置中未指定交易对时使用。
SCALER_TYPE = 'minmax'          # [字符串] 特征缩放器类型:
                                # 'minmax': MinMaxScaler，将特征缩放至0-1范围。
                                # 'standard': StandardScaler，将特征标准化 (均值为0，标准差为1)。
DATA_FETCH_LIMIT = 22222        # [整数] 在模型训练时，从Binance获取历史K线的最大条数。
TRAIN_RATIO = 0.7               # [浮点数, 0-1] 训练集在总获取数据中的占比。
VALIDATION_RATIO = 0.15         # [浮点数, 0-1] 验证集在总获取数据中的占比。
                                # 测试集占比将自动计算为: 1 - TRAIN_RATIO - VALIDATION_RATIO。



# --- SMOTE过采样全局配置 ---
SMOTE_GLOBAL_ENABLE = True      # [布尔值] 全局SMOTE开关，如果为False，所有目标都不会使用SMOTE
SMOTE_MIN_SAMPLES_THRESHOLD = 5 # [整数] 少数类样本的最小数量阈值，低于此值时跳过SMOTE
SMOTE_DEFAULT_K_NEIGHBORS = 4   # [整数] SMOTE默认的k_neighbors参数
SMOTE_RANDOM_STATE = 42         # [整数] SMOTE的随机种子，确保可重现性

# --- 完整阈值优化系统配置 ---
THRESHOLD_OPTIMIZATION_ENABLE = True           # [布尔值] 全局阈值优化开关
THRESHOLD_OPTIMIZATION_METHOD = "f1"           # [字符串] 默认阈值优化方法:
                                               # "f1": 最大化F1分数
                                               # "precision_recall": 基于精确率-召回率曲线的最优点
                                               # "youden": Youden指数 (敏感性 + 特异性 - 1)
                                               # "balanced": 平衡精确率和召回率
                                               # "precision_constrained_recall": 在精确率约束下最大化召回率
                                               # "grid_search": 网格搜索优化
                                               # "bayesian": 贝叶斯优化

THRESHOLD_SAVE_TO_METADATA = True              # [布尔值] 是否将最优阈值保存到模型元数据
THRESHOLD_DEFAULT_VALUE = 0.5                  # [浮点数] 默认决策阈值
THRESHOLD_USE_INDEPENDENT_VALIDATION = True    # [布尔值] 是否使用独立验证集进行阈值优化（推荐）
THRESHOLD_INDEPENDENT_VAL_RATIO = 0.15         # [浮点数] 独立验证集比例（从训练集中分离）

# --- 阈值优化算法参数 ---
THRESHOLD_GRID_SEARCH_RANGE = (0.1, 0.9)      # [元组] 网格搜索的阈值范围
THRESHOLD_GRID_SEARCH_STEP = 0.05              # [浮点数] 网格搜索的步长
THRESHOLD_BAYESIAN_N_TRIALS = 100              # [整数] 贝叶斯优化的试验次数
THRESHOLD_BAYESIAN_TIMEOUT = 300               # [整数] 贝叶斯优化超时时间（秒）

# --- 阈值约束参数 ---
THRESHOLD_MIN_PRECISION = 0.6                  # [浮点数] 最小精确率约束
THRESHOLD_MIN_RECALL = 0.3                     # [浮点数] 最小召回率约束
THRESHOLD_PRECISION_CONSTRAINT_FALLBACK = True # [布尔值] 约束不满足时是否使用最接近的阈值

# --- 阈值加载和应用配置 ---
THRESHOLD_LOAD_RETRY_COUNT = 3                 # [整数] 阈值加载失败重试次数
THRESHOLD_FALLBACK_TO_DEFAULT = True           # [布尔值] 加载失败时是否回退到默认阈值
THRESHOLD_VALIDATION_ENABLE = True             # [布尔值] 是否验证加载的阈值有效性

# --- 独立阈值投票机制配置 ---
USE_INDIVIDUAL_FOLD_THRESHOLDS = False         # [布尔值] 是否启用独立阈值投票机制
                                               # True: 每个fold使用自己的最优阈值进行决策，然后投票
                                               # False: 使用传统的平均概率+统一阈值机制
INDIVIDUAL_THRESHOLD_VOTE_RATIO = 0.5          # [浮点数] 投票通过所需的比例（0.5表示超过一半）
INDIVIDUAL_THRESHOLD_MIN_VOTES = 3             # [整数] 投票通过所需的最小票数

# --- 交互特征和高阶特征配置 ---
ENABLE_INTERACTION_FEATURES = True             # [布尔值] 是否启用交互特征（特征组合）
                                               # 包括：量价结合、波动率与趋势结合、K线实体与波动率结合等
ENABLE_HIGHER_ORDER_FEATURES = True            # [布尔值] 是否启用高阶特征（导数特征）
                                               # 包括：RSI变化率、MACD柱状图加速度、价格变化加速度等

# 🎯 新增：基于SHAP的智能特征选择配置 - 挖掘更深的"阿尔法"
SHAP_FEATURE_SELECTION_CONFIG = {
    "enable": True,                              # [布尔值] 是否启用SHAP特征选择
    "min_importance_threshold": 0.001,           # [浮点数] 最小重要性阈值，低于此值的特征将被过滤
    "top_k_features": None,                      # [整数或None] 保留前K个最重要的特征，None表示不限制
    "importance_percentile": 0.1,                # [浮点数] 重要性百分位数阈值（0.1表示过滤掉最低10%的特征）
    "save_selection_results": True,              # [布尔值] 是否保存特征选择结果
    "selection_results_file": "shap_feature_selection.json",  # [字符串] 选择结果保存文件名
    "use_cached_selection": True,                # [布尔值] 是否使用缓存的选择结果（如果存在）
    "fallback_to_rfe": True,                     # [布尔值] 当SHAP选择失败时是否回退到RFE
    "apply_to_base_models": True,                # [布尔值] 是否对基础模型应用SHAP特征选择
    "apply_to_meta_model": True,                 # [布尔值] 是否对元模型应用SHAP特征选择
}

# 🎯 新增：动态交易过滤器配置 - 解决"高波动盘整"等危险市场状态
DYNAMIC_TRADING_FILTER_CONFIG = {
    "enable": True,                              # [布尔值] 是否启用动态交易过滤器
    "enable_market_state_filter": True,         # [布尔值] 是否启用市场状态过滤
    "enable_volatility_filter": True,           # [布尔值] 是否启用波动率过滤
    "enable_trend_consistency_filter": True,    # [布尔值] 是否启用趋势一致性过滤
    "enable_confidence_adjustment": True,       # [布尔值] 是否启用置信度调整

    # 阻止信号的条件
    "block_on_high_vol_sideways": True,         # [布尔值] 高波动盘整时阻止信号
    "block_danger_score_threshold": 0.8,        # [浮点数] 危险评分阈值，超过则阻止
    "block_on_extreme_volatility": True,        # [布尔值] 极端波动时阻止
    "extreme_volatility_atr_threshold": 4.0,    # [浮点数] 极端波动ATR阈值

    # 降低置信度的条件
    "reduce_confidence_danger_threshold": 0.5,  # [浮点数] 危险评分阈值，超过则降低置信度
    "confidence_reduction_factor": 0.7,         # [浮点数] 置信度降低因子
    "reduce_on_moderate_volatility": True,      # [布尔值] 中等波动时降低置信度

    # 趋势一致性检查
    "require_trend_signal_alignment": True,     # [布尔值] 要求信号与趋势方向一致
    "trend_alignment_adx_threshold": 25,        # [浮点数] 趋势一致性检查的ADX阈值

    # 市场状态分析器配置
    "market_analyzer_config": {
        "high_volatility_atr_threshold": 2.5,
        "low_volatility_atr_threshold": 0.5,  # ✅ 正确！必须放在这个花括号里面
        "low_trend_strength_adx_threshold": 20,
        "strong_trend_adx_threshold": 30,
        "sideways_price_range_threshold": 0.05,
        "price_range_lookback_periods": 20,
    }
}

# --- 模拟盘与指令服务器集成 ---
SIMULATOR_INTEGRATION_ENABLED = True # [布尔值] 是否启用与模拟盘 (SimMain.py) 的集成。如果为True，预测信号会尝试发送到模拟盘。
SIMULATOR_API_URL = "http://127.0.0.1:5008/signal" # [字符串] 模拟盘接收预测信号的API端点URL。请确保端口与SimMain实例匹配。
COMMAND_SERVER_URL = "http://127.0.0.1:8080/internal_signal" # [字符串] 指令服务器 (CommandServer.py) 接收预测信号的API端点URL，用于对接Hamibot等外部执行系统。
# SIGNAL_SEND_COOLDOWN_SECONDS = 120 # [整数, 可选] 全局信号发送冷却时间（秒）。如果启用，预测系统在发送一个信号后，会等待这段时间才能发送下一个。prediction.py 中有更细致的per-target冷却。

# --- GUI 与声音相关 ---
ALERT_SOUND_ENABLED = True      # [布尔值] 是否启用交易信号的声音提示。
CUSTOM_UP_SIGNAL_SOUND_PATH = "sounds/signal_up.mp3"      # [字符串] 做多信号提示音文件的路径。
CUSTOM_DOWN_SIGNAL_SOUND_PATH = "sounds/signal_down.mp3"  # [字符串] 做空信号提示音文件的路径。
CUSTOM_SIGNAL_SOUND_DURATION_MS = 20000 # [整数] 交易信号提示音的播放时长 (毫秒)。pygame会在这个时长后尝试淡出音乐。

# --- GUI 颜色主题 ---
# 定义了预测程序GUI界面的各种颜色，用于美化和信息区分。
BG_COLOR = "#2E2E2E"        # 主要背景色
FG_COLOR = "#E0E0E0"        # 主要前景色 (文本颜色)
LABEL_BG_COLOR = "#3C3C3C"  # 标签控件的背景色
BUTTON_COLOR = "#4A4A4A"    # 按钮背景色
BUTTON_FG_COLOR = "#FFFFFF" # 按钮文字颜色
UP_COLOR = "#26A69A"        # 上涨趋势/信号的颜色 (通常为绿色系)
DOWN_COLOR = "#EF5350"      # 下跌趋势/信号的颜色 (通常为红色系)
NEUTRAL_COLOR = "#FFCA28"   # 中性状态/信号的颜色 (通常为黄色系)
TEXT_AREA_BG = "#1E1E1E"    # 滚动文本区域的背景色
TEXT_AREA_FG = "#D0D0D0"    # 滚动文本区域的文字颜色
ERROR_COLOR = "#FF5252"     # 错误信息的颜色
BLUE_COLOR_BUTTON = "#007BFF" # 一个标准的蓝色，用于某些特殊按钮

# --- 元模型 Stacking 配置 ---
ENABLE_META_MODEL_TRAINING = True     # [布尔值] 是否启用元模型的OOF (Out-of-Fold) 特征生成和后续的元模型训练流程。
META_MODEL_SAVE_DIR = "meta_model_data" # [字符串] 元模型本身、其使用的特征列表以及基础模型OOF预测数据保存的根目录。
ENABLE_META_MODEL_PREDICTION = True   # [布尔值] 是否在实时预测时使用元模型进行最终决策。
                                      # 如果为False，系统将按原方式独立预测和处理每个基础模型的信号。

# 定义用于元模型训练的基础模型列表
# 临时只使用BTC_15m_DOWN，直到BTC_15m_UP重新训练完成
BASE_MODELS_FOR_META = [BaseModelNames.BTC_15M_UP, BaseModelNames.BTC_15M_DOWN]  # [列表] 用于元模型训练的基础模型名称列表，必须与PREDICTION_TARGETS中的name字段匹配

# OOF (Out-of-Fold) 预测生成参数 (用于基础模型，为元模型准备特征)
META_MODEL_OOF_CV_FOLDS = 5                # [整数] 生成OOF预测时，对每个基础模型数据进行交叉验证的折数。
META_MODEL_OOF_LGBM_N_ESTIMATORS = 3000    # [整数] 在OOF生成过程中，每折训练基础模型时LightGBM的基础树数量 (通常配合早停使用)。
META_MODEL_OOF_LGBM_EARLY_STOPPING_ROUNDS = 75 # [整数] OOF生成过程中，每折LightGBM训练的早停轮数。
META_MODEL_OOF_VALID_FOR_EARLY_STOP_RATIO = 0.15 # [浮点数, 0-1] 在OOF的每一折训练中，从该折的训练数据中再划分出的、用于早停的内部验证集比例。
META_MODEL_OOF_MIN_SAMPLES_FOR_EARLY_STOP_VALID = 50 # [整数] OOF早停内部验证集所需的最少样本数。

# 元模型 (LightGBM 分类器) 自身训练参数
META_MODEL_LGBM_OBJECTIVE = 'multiclass'              # [字符串] 元模型的目标函数，'multiclass' 用于多分类。
META_MODEL_LGBM_NUM_CLASS = 3                       # [整数] 元模型预测的类别数量 (例如: 0=下跌, 1=上涨, 2=中性)。
META_MODEL_LGBM_METRIC = 'multi_logloss'            # [字符串 或 列表] 元模型训练时的评估指标，如 'multi_logloss' 或 'multi_error'。
META_MODEL_LGBM_CLASS_WEIGHT = 'balanced'  # [None/'balanced'/dict] 类别权重设置
# 🎯 当前策略: 重点提升Class 0召回率，同时保持对Class 1的关注
#
# 📊 优化历程:
# 1. ✅ 已完成LGBM本身优化 (类别权重 + Optuna指标优化)
# 2. ✅ 已完成概率质量提升 (custom_f1_class01_avg指标)
# 3. ✅ 已完成EMA特征增强 (新增7个高信息量EMA衍生特征)
# 4. ✅ 已完成阈值优化重新启用 (composite_score策略，500次试验)
# 5. 🎯 当前阶段: 调整类别权重，重点解决Class 0召回率问题
#
# 🔄 类别权重调整策略:
# 阶段1: {0: 1, 1: 1.5, 2: 1}     - 之前使用，侧重Class 1
# 阶段2: {0: 1.5, 1: 1.2, 2: 1}   - 🎯 当前使用，重点提升Class 0，保持Class 1关注
# 阶段3: {0: 1.8, 1: 1.2, 2: 1}   - 如果需要进一步提升Class 0
# 阶段4: {0: 1.5, 1: 1.5, 2: 1}   - 平衡提升Class 0和1
# 阶段5: {0: 2.0, 1: 1.5, 2: 1}   - 强化Class 0，适度保持Class 1
#
# 🎯 当前优化目标:
# - 主要目标: 显著提升Class 0 (下跌) 的召回率
# - 次要目标: 保持Class 1 (上涨) 的性能不大幅下降
# - 平衡目标: 维持整体LogLoss在合理范围内
# - 最终目标: 提升交易信号的全面性和盈利能力
META_MODEL_LGBM_N_ESTIMATORS = 2000                  # [整数] 元模型训练时的最大树数量 (保守设置: 50-150)。
META_MODEL_LGBM_LEARNING_RATE = 0.01                # [浮点数] 元模型的学习率 (保守设置: 0.01-0.05)。
META_MODEL_LGBM_NUM_LEAVES = 8                     # [整数] 元模型每棵树的最大叶子节点数 (保守设置: 7-15)。
META_MODEL_LGBM_MAX_DEPTH = 3                       # [整数] 元模型每棵树的最大深度 (保守设置: 3-5)。
META_MODEL_LGBM_REG_ALPHA = 5.0                     # [浮点数] 元模型L1正则化系数。
META_MODEL_LGBM_REG_LAMBDA = 5.0                    # [浮点数] 元模型L2正则化系数。
META_MODEL_LGBM_COLSAMPLE_BYTREE = 0.7              # [浮点数, 0-1] 元模型训练时每棵树的列采样比例。
META_MODEL_LGBM_SUBSAMPLE = 0.7                     # [浮点数, 0-1] 元模型训练时每棵树的行采样比例。
META_MODEL_LGBM_MIN_CHILD_SAMPLES = 50              # [整数] 元模型一个叶子节点上所需的最小数据量。
META_MODEL_LGBM_RANDOM_STATE = 2024                 # [整数] 元模型训练的随机种子。
META_MODEL_LGBM_VERBOSE = 1                         # [整数] LightGBM训练日志详细程度: -1静默, 0警告, 1信息。
META_MODEL_TRAIN_TEST_SPLIT_FOR_EVAL_RATIO = 0.2    # [浮点数, 0-1, 可选] 从OOF数据中划分出一部分作为元模型最终评估的测试集比例。
META_MODEL_LGBM_EARLY_STOPPING_ROUNDS_FINAL = 20    # [整数] 元模型最终训练时（如果使用了上述划分的验证集）的早停轮数。

# --- 元模型 SMOTE 配置 ---
META_MODEL_SMOTE_ENABLE = False                       # [布尔值] 是否为元模型启用SMOTE过采样
META_MODEL_SMOTE_K_NEIGHBORS = 4                     # [整数] 元模型SMOTE的k_neighbors参数
META_MODEL_SMOTE_MIN_SAMPLES_THRESHOLD = 5           # [整数] 元模型SMOTE的最小样本阈值
META_MODEL_SMOTE_RANDOM_STATE = 42                   # [整数] 元模型SMOTE的随机种子

# --- 元模型 Optuna 配置 ---
META_MODEL_OPTUNA_ENABLE = False               # [布尔值] 是否为元模型启用Optuna超参数优化
# 🎯 重新启用Optuna，专注于Class 1性能优化:
# - 使用增强的custom_f1_class01_avg指标
# - 结合类别权重调整，寻找最佳超参数组合
# - 确保指标计算稳定可靠
META_MODEL_OPTUNA_N_TRIALS = 50               # [整数] Optuna为元模型运行时尝试的试验次数 (可以先设小一点测试，例如20-50)。
META_MODEL_OPTUNA_TIMEOUT = None              # [整数 或 None] Optuna运行的最大秒数。None表示不限制时间。
META_MODEL_OPTUNA_METRIC = 'simulated_profit_composite'   # [字符串] 元模型Optuna优化指标
# 🎯 革命性改进：使用基于盈利能力的优化目标，而非技术指标
#
# 💰 盈利能力导向指标 (推荐):
# 1. 'simulated_profit_expected' (direction='maximize') - 直接优化期望收益，最直接的盈利目标
# 2. 'simulated_profit_risk_adjusted' (direction='maximize') - 风险调整收益，考虑交易频率
# 3. 'simulated_profit_composite' (direction='maximize') - 复合盈利指标，综合收益、胜率、频率 (推荐)
# 4. 'simulated_profit_sharpe' (direction='maximize') - 基于模拟交易的夏普比率
#
# 📊 技术指标 (向后兼容):
# 5. 'custom_f1_class01_avg' (direction='maximize') - Class 0和1的F1分数平均值
# 6. 'multi_logloss' (direction='minimize') - 基础LogLoss优化，提升整体概率质量
# 7. 'custom_precision_class01_avg' (direction='maximize') - Class 0和1的精确率平均值
# 8. 'custom_precision_class01_weighted' (direction='maximize') - 加权精确率，更侧重Class 1 (做多)
# 9. 'custom_f1_class01_weighted' (direction='maximize') - 加权F1分数，更侧重Class 1 (做多)
# 10. 'custom_recall_class01_avg' (direction='maximize') - Class 0和1的召回率平均值
#
# 🔧 传统指标:
# - 'macro_f1_score', 'weighted_f1_score', 'val_accuracy'
META_MODEL_OPTUNA_DIRECTION = "maximize"      # [字符串] Optuna优化目标指标的方向: "maximize" 或 "minimize"。
META_MODEL_OPTUNA_CV_FOLDS = 3                # [整数] Optuna在评估每个试验参数集时，内部进行交叉验证的折数 (使用TimeSeriesSplit)。
META_MODEL_OPTUNA_LGBM_N_ESTIMATORS_MAX = 300 # [整数] Optuna单次试验中，元模型LGBM允许训练的最大树数量 (通常配合早停)。
META_MODEL_OPTUNA_LGBM_EARLY_STOPPING_ROUNDS = 30 # [整数] Optuna单次试验中，元模型LGBM训练的早停轮数。

# --- 元模型特征工程配置 (高优先级优化) ---
META_MODEL_FEATURE_ENGINEERING_CONFIG = {
    'enable_prob_diff': True,           # [布尔值] 是否启用概率差异特征 (meta_prob_diff_up_vs_down)
    'enable_prob_sum': True,            # [布尔值] 是否启用概率总和特征 (meta_prob_sum_up_down)
    'enable_lag_features': True,        # [布尔值] 是否启用滞后特征 (meta_lag1_*)
    'enable_change_features': False,    # [布尔值] 高优先级优化：禁用无效的变化类特征 (meta_change1_*)
    'enable_global_features': True,     # [布尔值] 是否启用全局市场状态特征
    'change_feature_method': 'diff',    # [字符串] 变化特征计算方法: 'diff', 'pct_change', 'ratio'
    'lag_periods': [1],                 # [列表] 滞后期数列表，默认只使用1期滞后

    # 🎯 新增：上下文特征工程配置
    'enable_context_features': True,    # [布尔值] 是否启用基础模型上下文特征
    'enable_model_divergence': True,    # [布尔值] 是否启用基础模型间分歧度特征
    'enable_confidence_features': True, # [布尔值] 是否启用置信度相关特征
    'enable_market_regime_features': True, # [布尔值] 是否启用市场状态特征（波动率、趋势强度等）
}
META_MODEL_OPTUNA_LGBM_EVAL_METRIC = 'multi_logloss' # [字符串] Optuna单次试验中，元模型LGBM早停所依据的评估指标。

# --- 元模型决策阈值优化配置 ---
META_MODEL_THRESHOLD_OPTIMIZATION_ENABLE = False          # [布尔值] 是否启用元模型决策阈值优化
META_MODEL_THRESHOLD_OPTIMIZATION_METHOD = 'optuna'      # [字符串] 优化方法: 'grid_search' 或 'optuna'
META_MODEL_THRESHOLD_OPTIMIZATION_N_TRIALS = 1000        # [整数] Optuna试验次数 (增加到1000以充分探索参数空间)
META_MODEL_THRESHOLD_OPTIMIZATION_TIMEOUT = 3600         # [整数] Optuna优化超时时间(秒)，60分钟
META_MODEL_THRESHOLD_OPTIMIZATION_STRATEGY = 'composite_score'  # [字符串] 优化策略:
# 🎯 核心优化策略 (已优化，推荐使用):
# - 'profit_first': 优先最大化总盈利 (隐式鼓励更多正收益交易)
# - 'balanced_profit_frequency': 风险调整收益 (收益 × √交易次数)
# - 'frequency_under_winrate_constraint': 胜率约束下最大化交易频率 (胜率>55%时优先频率)
# - 'composite_score': 复杂效用函数，正收益时鼓励频率，负收益时重惩罚 (最推荐)
# - 'risk_adjusted': 风险调整收益 (考虑夏普比率)
# - 'total_expected_profit': 综合指标 (总收益+风险调整+胜率×频率)
#
# 🔧 传统策略 (向后兼容):
# - 'expected_profit': 纯期望收益最大化
# - 'risk_adjusted_return': 风险调整收益
# - 'win_rate_weighted': 胜率加权的期望收益
# - 'f1_weighted': F1分数加权的期望收益
# - 'sharpe_like': 类似夏普比率

# --- 阈值搜索范围配置 ---
META_MODEL_THRESHOLD_MIN = 0.1                           # [浮点数] 决策阈值最小值 (向下扩展)
META_MODEL_THRESHOLD_MAX = 0.9                           # [浮点数] 决策阈值最大值 (向上扩展)
META_MODEL_CONFIDENCE_GAP_MIN = 0.0                      # [浮点数] 置信度差值最小值 (从0开始)
META_MODEL_CONFIDENCE_GAP_MAX = 0.3                      # [浮点数] 置信度差值最大值 (降低以探索更小GAP值)

# --- 优化约束配置 (强制执行) ---
META_MODEL_MIN_TRADES_CONSTRAINT = 20                    # [整数] 最小交易次数约束 (提高到20次)
META_MODEL_MIN_WIN_RATE_CONSTRAINT = 0.4                 # [浮点数] 最小胜率约束 (40%)
META_MODEL_MAX_CONSECUTIVE_LOSSES = 10                   # [整数] 最大连续亏损次数限制
META_MODEL_ENABLE_EARLY_STOPPING = True                  # [布尔值] 是否启用早停机制

# --- Optuna优化增强配置 ---
# META_MODEL_OPTUNA_DIRECTION 已在上面定义，这里不重复
META_MODEL_OPTUNA_SAMPLER_N_STARTUP_TRIALS = 50          # [整数] TPE采样器启动试验数 (前10%用于随机探索)
META_MODEL_OPTUNA_EARLY_STOP_PATIENCE = 30               # [整数] 早停耐心值 (连续N次无改进则停止)

# 定义元模型Optuna试验的参数搜索空间 (示例)
META_MODEL_OPTUNA_PARAM_GRID = {
    'learning_rate': ('float', 0.005, 0.05, True),  # (type, low, high, log_scale)
    'num_leaves': ('int', 5, 20),                   # (type, low, high)
    'max_depth': ('int', 3, 7),
    'reg_alpha': ('float', 0.1, 10.0, True),
    'reg_lambda': ('float', 0.1, 10.0, True),
    'colsample_bytree': ('float', 0.5, 0.9),
    'subsample': ('float', 0.5, 0.9),
    'min_child_samples': ('int', 10, 50)
    # 'class_weight': ('categorical', [None, 'balanced']) # 如果也想让Optuna选，但通常先固定为'balanced'
}



# --- 元模型输入特征与预过滤配置 ---
# 🎯 丰富化元模型特征输入：不仅包含基础模型概率，还包含决策上下文特征
META_MODEL_INPUT_FEATURES_CONFIG = [
    # --- UP模型特征 ---
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": ProbabilityTypes.P_FAVORABLE,
        "meta_feature_name": "oof_proba_BTC_15m_UP_pfavorable"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.atr_percent",
        "meta_feature_name": "feat_atr_pct_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.adx_value",
        "meta_feature_name": "feat_adx_val_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.prediction_confidence",
        "meta_feature_name": "feat_confidence_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.rsi_value",
        "meta_feature_name": "feat_rsi_val_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.volume_ratio",
        "meta_feature_name": "feat_volume_ratio_up_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_UP,
        "prob_type": "context_features.price_change_1p",
        "meta_feature_name": "feat_price_change_1p_up_model"
    },

    # --- DOWN模型特征 ---
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": ProbabilityTypes.P_FAVORABLE,
        "meta_feature_name": "oof_proba_BTC_15m_DOWN_pfavorable"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.atr_percent",
        "meta_feature_name": "feat_atr_pct_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.adx_value",
        "meta_feature_name": "feat_adx_val_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.prediction_confidence",
        "meta_feature_name": "feat_confidence_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.rsi_value",
        "meta_feature_name": "feat_rsi_val_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.volume_ratio",
        "meta_feature_name": "feat_volume_ratio_down_model"
    },
    {
        "base_model_name": BaseModelNames.BTC_15M_DOWN,
        "prob_type": "context_features.price_change_1p",
        "meta_feature_name": "feat_price_change_1p_down_model"
    }
]

# 元模型实时预测的预过滤配置
META_MODEL_PRE_FILTER_UP_CONFIG = {
    "base_model_name": BaseModelNames.BTC_15M_UP,    # 必须是 META_MODEL_INPUT_FEATURES_CONFIG 中某一项的 base_model_name
    "prob_type": ProbabilityTypes.P_FAVORABLE,  # 用于预过滤判断的概率类型
    "threshold": 0.05
}

META_MODEL_PRE_FILTER_DOWN_CONFIG = {
    "base_model_name": BaseModelNames.BTC_15M_DOWN,  # 必须是 META_MODEL_INPUT_FEATURES_CONFIG 中某一项的 base_model_name
    "prob_type": ProbabilityTypes.P_FAVORABLE,
    "threshold": 0.05
}
# 预过滤逻辑: 如果 META_MODEL_PRE_FILTER_UP_CONFIG 指定的概率 < threshold AND META_MODEL_PRE_FILTER_DOWN_CONFIG 指定的概率 < threshold，则预判为中性。

# 定义在创建元模型的目标变量时，使用哪个基础模型的配置参数
# (如 prediction_periods, target_threshold) 来确定“明确上涨/下跌/中性”的定义。
BASE_CONFIG_FOR_META_TARGET_DEFINITION = BaseModelNames.BTC_15M_UP # 或者 BaseModelNames.BTC_15M_DOWN，取决于你认为哪个更能代表元目标周期。

# 元模型相关的特殊名称定义
META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS = "MetaModel_BTC_15m" # [字符串] 用于在 dynamic_params.json 中查找元模型相关动态参数的键名。
META_MODEL_GUI_DISPLAY_NAME = BaseModelNames.META_SIGNAL_BTC # [字符串] 在GUI中，代表元模型综合预测结果的那个显示条目的名称。
META_MODEL_TARGET_NAME = BaseModelNames.META_SIGNAL_BTC # [字符串] 元模型的目标名称，用于训练和预测

# 元模型决策阈值配置 - 将概率转化为行动决策
META_SIGNAL_UP_THRESHOLD = 0.2      # [浮点数, 0-1] 元模型做多的概率阈值，P(上涨) >= 此值时考虑做多
META_SIGNAL_DOWN_THRESHOLD = 0.2    # [浮点数, 0-1] 元模型做空的概率阈值，P(下跌) >= 此值时考虑做空
META_SIGNAL_CONFIDENCE_GAP = 0.07    # [浮点数, 0-1] 置信度差值要求，P(目标方向) - P(中性) >= 此值时才执行
META_SIGNAL_USE_CONFIDENCE_GAP = True  # [布尔值] 是否启用置信度差值检查

# --- 全局市场状态配置 (用于元模型特征工程) ---
# 这些参数用于计算全局的趋势和波动率指标，作为元模型的额外特征
GLOBAL_MARKET_STATE_CONFIG = {
    "symbol": "BTCUSDT",                             # [字符串] 全局市场状态分析的交易对
    "timeframe": "15m",                              # [字符串] 全局市场状态分析的时间框架 (修改为15m以确保动态变化)
    "trend_indicator_type": "both",                  # [字符串] 全局趋势指标类型: 'adx', 'ema_cross', 或 'both' (修复：启用ADX计算同时保持EMA)
    "trend_adx_period": 14,                          # [整数] 全局ADX计算周期
    "trend_adx_strength_threshold": 20,              # [整数] 全局ADX强趋势阈值 (修复：从25降到20)
    "trend_adx_threshold": 15,                       # [整数] 全局ADX趋势存在阈值 (修复：从20降到15)
    "trend_ema_short_period": 21,                    # [整数] 全局短期EMA周期
    "trend_ema_long_period": 50,                     # [整数] 全局长期EMA周期
    # 🎯 新增：EMA衍生特征配置
    "ema_slope_period": 5,                           # [整数] EMA斜率计算周期 (用于计算EMA变化率)
    "volatility_atr_period": 14,                     # [整数] 全局ATR计算周期
    "volatility_min_atr_percent": 0.08,              # [浮点数] 全局最小ATR百分比阈值
    "volatility_max_atr_percent": 1.5,               # [浮点数] 全局最大ATR百分比阈值
    "data_fetch_limit": 500,                         # [整数] 获取全局市场状态数据的K线数量 (增加以确保EMA预热)
}

# 🚨 ADX调试开关 (修复全局ADX问题时启用)
GLOBAL_ADX_DEBUG_VERBOSE = False  # [布尔值] 启用全局ADX计算的详细调试输出

# --- EMA调试配置 ---
EMA_DEBUG_VERBOSE = False                            # [布尔值] 是否启用详细的EMA调试日志输出
                                                     # True: 每次EMA计算都输出详细信息 (仅用于调试)
                                                     # False: 只在必要时输出日志 (生产环境推荐)

# 为元模型定义静态的凯利公式参数默认值。
# 这些值可以被 `dynamic_params.json` 文件中同名目标下的参数覆盖。
# 其结构模仿 `PREDICTION_TARGETS` 中单个目标的 "kelly_config_params" 部分。
META_MODEL_STATIC_KELLY_CONFIG = {
    "name": META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS, # [字符串] 必须与上面的虚拟目标名一致
    "trade_amount_strategy": "kelly_config", # [字符串] 明确指定元模型信号金额计算策略为凯利
    "kelly_config_params": {
        "payout_ratio_b": 0.85,                         # [浮点数] 盈亏比 (赔率)，通常与基础模型一致或接近。
        "win_rate_p_estimate": 0.56,                    # [浮点数] 🎯 初始预估胜率 (将被动态胜率替代)
        "virtual_total_capital_for_kelly": 50.0,        # [浮点数] 用于元模型凯利计算的虚拟总资本。
        "max_kelly_fraction_f": 0.20,                   # [浮点数, 0-1] 🎯 保守的分数凯利：最大25%凯利比例 (原0.07太保守，0.25更平衡)
        "min_bet_kelly": 5.0,                           # [浮点数] 凯利公式计算金额的下限。
        "max_bet_kelly": 250.0,                         # [浮点数] 凯利公式计算金额的上限。
        "enable_initial_conservative_betting": True,    # [布尔值] 是否启用初始保守下注期。
        "initial_conservative_trades_count": 25,        # [整数] 保守下注期的交易次数。
        "initial_conservative_bet_amount": 5.0,         # [浮点数] 保守期内的固定下注金额。
        "min_bet_if_kelly_negative": 5.0,               # [浮点数] 当凯利分数计算为负或零时使用的最小下注额。
        "enabled": True                                 # [布尔值] 此凯利配置是否启用。元模型信号是否实际发送还受全局和元模型自身动态参数的 `master_signal_sending_enabled` 和 `enabled` 控制。
    }
}

# --- 全局硬性开关 (这些开关会覆盖目标配置中的对应设置) ---
GLOBAL_HARD_SWITCHES = {
    'smote_global_enable': SMOTE_GLOBAL_ENABLE,      # 全局SMOTE开关，如果为False，所有目标都不会使用SMOTE
    'threshold_optimization_global_enable': THRESHOLD_OPTIMIZATION_ENABLE,  # 全局阈值优化开关
    'meta_model_training_enable': ENABLE_META_MODEL_TRAINING,  # 全局元模型训练开关
    'meta_model_prediction_enable': ENABLE_META_MODEL_PREDICTION,  # 全局元模型预测开关
    'simulator_integration_enabled': SIMULATOR_INTEGRATION_ENABLED,  # 全局模拟盘集成开关
    'alert_sound_enabled': ALERT_SOUND_ENABLED,  # 全局声音提示开关
}

# --- 全局默认值 (目标配置可以覆盖这些值) ---
GLOBAL_DEFAULTS = {
    'symbol': SYMBOL,
    'scaler_type': SCALER_TYPE,
    'data_fetch_limit': DATA_FETCH_LIMIT,
    'train_ratio': TRAIN_RATIO,
    'validation_ratio': VALIDATION_RATIO,
    'smote_min_samples_threshold': SMOTE_MIN_SAMPLES_THRESHOLD,
    'smote_default_k_neighbors': SMOTE_DEFAULT_K_NEIGHBORS,
    'smote_random_state': SMOTE_RANDOM_STATE,
    'threshold_default_value': THRESHOLD_DEFAULT_VALUE,
    'threshold_independent_val_ratio': THRESHOLD_INDEPENDENT_VAL_RATIO,
}

# --- 配置模板定义 ---
# 基础模型配置模板，减少冗余配置
BASE_MODEL_TEMPLATE = {
    # 基本信息模板
    'interval': '15m',
    'symbol': SYMBOL,
    'prediction_periods': [2],
    'prediction_minutes_display': 30,
    'drop_neutral_targets': False,
    'device_type': DeviceType.GPU.value,
    'prediction_trigger_type': TriggerType.KLINE_CLOSE.value,

    # 特征工程开关模板
    'enable_price_change': True,
    'enable_volume': True,
    'enable_candle': True,
    'enable_ta': True,
    'enable_time': True,
    'enable_fund_flow': True,
    'enable_mtfa': True,
    'enable_pattern_recognition': False,
    'enable_trend_slope': True,

    # 通用参数模板
    'price_change_periods': [1, 2, 3, 5, 10],
    'enable_time_trigonometric': True,
    'enable_adx_trend_features': True,
    'enable_ema_trend_features': True,
    'enable_ta_derived_features': True,
    'mtfa_timeframes': ['30m', '1h', '4h'],
    'mtfa_feature_lookback_periods': 200,
    'mtfa_specific_lookbacks': {},
    'mtfa_min_bars_to_fetch': 50,
    'mtfa_min_bars_for_calc': 50,
    'mtfa_fetch_buffer': 10,

    # 高级数据处理配置模板
    'enable_intelligent_nan_processing': True,
    'enable_safe_fill_nans': True,
    'min_historical_bars_for_prediction': 100,
    'enable_advanced_feature_validation': True,

    # RFE配置模板
    'rfe_enable': False,
    'rfe_cv_folds': 3,
    'rfe_step': 1,
    'rfe_estimator_n_estimators': 100,
    'rfe_estimator_learning_rate': 0.1,
    'rfe_estimator_num_leaves': 31,
    'rfe_min_features_to_select': 10,

    # 特征选择配置模板
    'importance_thresholding_enable': False,
    'importance_threshold_value': 22,
    'importance_top_n_features': 52,
    'importance_model_n_estimators': 200,
    'learning_rate_initial_imp': 0.05,
    'num_leaves_initial_imp': 15,
    'max_depth_initial_imp': 5,
    'reg_alpha_initial_imp': 5.0,
    'reg_lambda_initial_imp': 5.0,
    'colsample_bytree_initial_imp': 0.7,
    'subsample_initial_imp': 0.7,
    'min_child_samples_initial_imp': 30,
    'metric_initial_imp': 'binary_logloss',

    # Optuna配置模板
    'optuna_enable': False,
    'optuna_n_trials': 100,
    'optuna_timeout': None,
    'optuna_direction': 'maximize',
    'optuna_cv_folds': 3,
    'optuna_trial_n_estimators_max': 1500,
    'optuna_trial_early_stopping_rounds': 50,
    'optuna_trial_eval_metric': 'binary_logloss',

    # LightGBM基础配置模板
    'objective': 'binary',
    'metric': 'binary_logloss',
    'class_weight': {0: 1, 1: 1.5},  # LightGBM类别权重参数：用于处理类别不平衡问题。{0: 1, 1: 1.5} 表示正类权重为负类的1.5倍。
                                     # 注意：此参数在Optuna超参数优化网格中未包含，因此是固定参数。如需优化，请在Optuna配置中添加相应的搜索空间。
    'boosting_type': 'gbdt',
    'random_state': 42,
    'n_estimators': 5000,
    'early_stopping_rounds': 200,
    'verbose': -1,
    'verbosity': -1,
    'ensemble_runs': 1,  # 已弃用参数：原用于控制集成运行次数，现已被 ensemble_cv_folds 替代。保留用于向后兼容，建议使用 ensemble_cv_folds 控制集成策略
    'ensemble_cv_folds': 5,
    'subsample_freq': 1,

    # SMOTE配置模板
    'smote_enable': True,
    'smote_k_neighbors': 5,
    'smote_min_samples_threshold': 5,
    'smote_random_state': 42,

    # 阈值优化配置模板
    'threshold_optimization_enable': False,
    'threshold_save_to_metadata': True,
    'threshold_default_value': 0.5,
    'threshold_use_independent_validation': True,
    'threshold_independent_val_ratio': 0.15,
    'threshold_precision_constraint_fallback': True,

    # 概率校准配置模板
    'enable_probability_calibration': True,
    'calibration_brier_improvement_threshold': 0.0001,

    # SHAP分析配置模板
    'enable_shap_analysis': True,
    'enable_shap_for_live_prediction': False,

    # 信号过滤配置模板 (关闭基础模型过滤，让元模型处理)
    'enable_trend_detection': False,
    'trend_detection_timeframe': '30m',
    'trend_indicator_type': TrendIndicatorType.ADX.value,
    'trend_filter_strategy': 'filter_only',
    'trend_chase_confidence_boost': 0.05,
    'enable_volatility_filter': False,
    'volatility_filter_timeframe': '30m',
    'volatility_atr_period': 14,
    'volatility_min_atr_percent': 0.1,
    'volatility_max_atr_percent': 1.2,
    'enable_dynamic_threshold': True,

    # OOF参数模板
    'meta_model_oof_cv_folds': 5,
    'meta_model_oof_n_estimators_large': 3000,
    'meta_model_oof_early_stopping_rounds': 100,
    'meta_model_oof_early_stop_eval_ratio': 0.15,
    'meta_model_oof_min_samples_for_eval': 50,
}

# --- 预测目标配置列表 (PREDICTION_TARGETS) ---
# 这是一个列表，其中每个元素都是一个字典，定义了一个独立的预测目标（通常是一个基础模型）。
PREDICTION_TARGETS = [
    {
        # --- 目标基本信息 ---
        "name": "BTC_15m_UP",                           # [字符串] 目标名称，必须唯一。用于GUI识别、日志记录、模型文件命名等。
        "interval": "15m",                              # [字符串] 此目标模型分析和预测所基于的主要K线时间周期 (例如 '1m', '5m', '15m', '1h')。
        "symbol": "BTCUSDT",                            # [字符串] 此目标针对的交易对。
        "prediction_periods": [2],                      # [列表, 包含整数] 预测未来多少个 'interval' 定义的周期后的价格方向。例如，[2] 表示预测未来 2 * 15分钟 = 30分钟后的方向。
        "prediction_minutes_display": 30,               # [整数] 在GUI中更友好地显示此目标的预测时效 (例如 "30分钟预测")。
        "model_save_dir": "trained_models_btc_15m_up",  # [字符串] 存储此目标训练产物（模型、scaler、特征列表、元数据等）的目录名。
        "target_variable_type": "UP_ONLY",              # [字符串] 目标变量的类型:
                                                        #   "UP_ONLY": 模型预测是否“明确上涨”。目标变量为1代表明确上涨，0代表非明确上涨（即横盘或下跌）。
                                                        #   "DOWN_ONLY": 模型预测是否“明确下跌”。目标变量为1代表明确下跌，0代表非明确下跌（即横盘或上涨）。
                                                        #   "BOTH": （如果仍在使用此逻辑）通常指模型预测上涨（1）或下跌（0），可能需要处理中性情况。
        "drop_neutral_targets": False,                  # [布尔值] 在创建目标变量时，是否丢弃“中性”样本。对于 "UP_ONLY" 或 "DOWN_ONLY"，这通常设为False，因为中性情况已被归为0类。
        "device_type": 'gpu',                           # [字符串] 模型训练和预测时使用的设备: 'cpu' 或 'gpu' (如果LightGBM GPU版本已安装并配置)。
        "prediction_trigger_type": "kline_close",       # [字符串] 此目标预测的主要触发方式。 "kline_close" 表示当对应symbol和interval的K线关闭时触发。 "apscheduler_driven" 表示主要由APScheduler任务驱动。
        # "apscheduler_job_enabled": True,                # [布尔值, 可选] 是否为这个目标启用APScheduler定时作业。
        # "apscheduler_trigger_type": "cron",             # [字符串, 可选] APScheduler触发类型: 'cron', 'interval', 'date'。
        # "apscheduler_cron_config": {"minute": "*/15"},  # [字典或字符串, 可选] 如果是cron类型，具体的cron配置。例如 {"minute": "5"} 表示每小时的第5分钟，{"minute": "*/15"} 表示每15分钟。

        # --- 特征工程参数 ---
        # 🔧 Optimized Switches (优化开关):
        "enable_price_change": True,                    # [布尔值] 是否启用价格变动百分比特征。
        "enable_volume": True,                          # [布尔值] 是否启用成交量相关特征。
        "enable_candle": True,                          # [布尔值] 是否启用基于K线形状的特征。
        "enable_ta": True,                              # [布尔值] 是否启用基于pandas-ta库计算的技术分析指标。
        "enable_time": True,                            # [布尔值] 是否启用基于时间戳的特征。
        "enable_fund_flow": True,                       # [布尔值] 是否启用资金流相关特征。
        "enable_mtfa": True,                            # [布尔值] 是否启用多时间框架分析 (MTFA) 特征。
        "enable_pattern_recognition": True,             # [布尔值] 是否启用高级K线形态识别。
        "enable_trend_slope": True,                     # [布尔值] 是否启用趋势斜率分析特征。

        # ⚙️ Optimized Parameters (优化参数):
        "volume_avg_period": 33,                         # [整数] 成交量移动平均周期。
        "atr_period": 12,                               # [整数] Average True Range (ATR) 的周期。
        "rsi_period": 22,                               # [整数] Relative Strength Index (RSI) 的周期。
        "willr_period": 22,                             # [整数] Williams %R 周期参数 (匹配训练模型)
        "cci_period": 22,                               # [整数] CCI 周期参数 (匹配训练模型)
        "macd_fast": 9,                                # [整数] MACD快线EMA周期。
        "macd_slow": 32,                                # [整数] MACD慢线EMA周期。
        "macd_sign": 11,                                 # [整数] MACD信号线EMA周期。
        "stoch_k": 23,                                  # [整数] Stochastic %K周期。
        "stoch_d": 2,                                  # [整数] Stochastic %D周期。
        "stoch_smooth_k": 4.9,                            # [整数] Stochastic %K平滑周期。
        "fund_flow_ratio_smoothing_period": 8,         # [整数] 资金流指标平滑处理周期 (匹配训练模型)。
        "target_threshold": 0.002,                   # [浮点数] 目标变量阈值。
        "hma_period": 15,                               # [整数] Hull Moving Average (HMA) 周期。
        "kc_period": 13,                                # [整数] Keltner Channel EMA中线周期。
        "kc_atr_period": 13,                            # [整数] Keltner Channel ATR计算周期。
        "kc_multiplier": 2.115598740558161,             # [浮点数] Keltner Channel ATR倍数。
        "signal_threshold": 0.7957296741575389,                   # [浮点数] 模型预测信号阈值。
        "trend_adx_period": 17,                         # [整数] ADX趋势指标计算周期。
        "trend_adx_threshold": 34,                      # [整数] ADX趋势强度阈值。
        "trend_ema_short_period": 19,                   # [整数] 趋势分析短期EMA周期。
        "trend_ema_long_period": 56,                    # [整数] 趋势分析长期EMA周期。
        "trend_slope_period_1": 3,                      # [整数] 第一个趋势斜率计算周期。
        "trend_slope_period_2": 12,                     # [整数] 第二个趋势斜率计算周期。
        # 🎯 新增：UP模型专用EMA距离特征参数
        "ema_short_period_up": 10,                      # [整数] UP模型短期EMA周期，用于计算均线距离特征。
        "ema_long_period_up": 30,                       # [整数] UP模型长期EMA周期，用于计算均线距离特征。
        # 🎯 新增：UP模型专用布林带突破强度特征参数
        "bb_period": 20,                                # [整数] 布林带计算周期。
        "bb_std": 2.0,                                  # [浮点数] 布林带标准差倍数。
        # 🎯 新增：时间框架敏感度特征参数
        "enable_timeframe_sensitivity": True,           # [布尔值] 是否启用时间框架敏感度特征。
        "tf_sensitivity_reference_timeframe": "4h",     # [字符串] 参考时间框架（用于对比）。
        "tf_sensitivity_features": ["rsi", "close_pos_in_candle", "macd", "volume_ratio"],  # [列表] 要计算对比的特征类型。
        "doji_threshold": 0.12785813365104795,                     # [浮点数] 十字星形态阈值。
        "hammer_body_ratio": 0.3940528078916167,                  # [浮点数] 锤子线实体比例阈值。
        "hammer_shadow_ratio": 2.431616506840809,                # [浮点数] 锤子线影线比例阈值。
        "marubozu_threshold": 0.904916951086358,                 # [浮点数] 光头光脚线阈值。
        "spinning_top_threshold": 0.4785481038750671,             # [浮点数] 陀螺线阈值。
        "engulfing_body_multiplier": 1.450922007228928,               # [浮点数] 吞没形态倍数阈值。
        "morning_evening_star_body_ratio": 0.20214578907876324,         # [浮点数] 启明星/黄昏星比例阈值。
        "doji_threshold_batac": 0.08397705355404736,                # [浮点数] 多K线十字星阈值。

        # 📊 Additional Configuration (附加配置):
        "price_change_periods": [1, 2, 3, 5, 10],       # [列表] 价格变动计算周期。
        "cci_constant": 0.0025,                         # [浮点数] CCI计算常数 (匹配UP模型)。
        "enable_time_trigonometric": True,              # [布尔值] 是否启用时间三角函数编码。
        "enable_adx_trend_features": True,              # [布尔值] 是否启用ADX趋势特征。
        "enable_ema_trend_features": True,              # [布尔值] 是否启用EMA交叉趋势特征。
        "enable_ta_derived_features": True,             # [布尔值] 是否启用技术指标衍生特征。
        "mtfa_timeframes": ['30m','1h', '4h'],          # [列表] MTFA时间框架。
        "mtfa_feature_lookback_periods": 200,           # [整数] MTFA特征回看周期。
        "mtfa_specific_lookbacks": {},                  # [字典] MTFA特定回看周期。
        "mtfa_min_bars_to_fetch": 50,                   # [整数] MTFA最小K线数量。
        "mtfa_min_bars_for_calc": 50,                   # [整数] MTFA计算最小K线数量。
        "mtfa_fetch_buffer": 10,                        # [整数] MTFA数据获取缓冲区。

        # --- 高级数据处理配置 ---
        "enable_intelligent_nan_processing": True,      # [布尔值] 是否启用智能NaN/Inf处理，根据特征类型选择合适的默认值。
        "enable_safe_fill_nans": True,                  # [布尔值] 是否启用安全的NaN填充方法，仅使用历史数据进行填充。
        "min_historical_bars_for_prediction": 100,      # [整数] 实时预测时所需的最小历史K线数量。
        "enable_advanced_feature_validation": True,     # [布尔值] 是否启用高级特征验证和错误处理。

        # --- 目标变量定义 和 预测信号阈值 ---
        "target_threshold": 0.002,                      # [浮点数] 定义“明确方向变动”的价格百分比阈值。例如0.002代表0.2%。用于创建目标变量 (上涨/下跌)。
        "signal_threshold": 0.4,                        # [浮点数, 0-1] 模型输出的预测概率需要超过此阈值，才初步认为是一个有效的交易信号 (此阈值可被后续的动态阈值逻辑调整)。

        # --- RFE (Recursive Feature Elimination) 配置 ---
        "rfe_enable": False,                            # [布尔值] 是否启用RFE递归特征消除来进行特征选择。
         "rfe_cv_folds": 3,                              # [整数, 可选] 如果启用RFE，RFE内部交叉验证的折数。
         "rfe_step": 1,                                  # [整数或浮点数, 可选] RFE每次迭代移除特征的数量或百分比。
         "rfe_scoring": "f1",                      # [字符串, 可选] RFE中评估特征子集好坏所用的评分指标。
         "rfe_estimator_n_estimators": 100,              # [整数, 可选] RFE内部使用的LightGBM评估器的树数量。
         "rfe_estimator_learning_rate": 0.1,             # [浮点数, 可选] RFE内部LightGBM评估器的学习率。
         "rfe_estimator_num_leaves": 31,                 # [整数, 可选] RFE内部LightGBM评估器的叶子数。
         "rfe_min_features_to_select": 10,               # [整数, 可选] RFE最终选择的最小特征数量。

        # --- 特征选择：基于初始模型重要性进行筛选 ---
        "importance_thresholding_enable": True,         # [布尔值] 是否在RFE之后（或直接在全部特征上）再进行一次基于LightGBM初始重要性的特征筛选。
        "importance_threshold_value": 0,               # [整数] 如果 `importance_top_n_features` 未设置或为None，则移除重要性绝对值低于此阈值的特征。
        "importance_top_n_features": 80,                # [整数 或 None] 如果设置了此值，则优先选择重要性最高的Top N个特征。如果为None，则使用 `importance_threshold_value`。
        "importance_model_n_estimators": 300,           # [整数] 用于获取初始特征重要性的临时LightGBM模型的树数量。
        # 以下为用于获取初始重要性的LGBM模型的超参数，可以根据DOWN模型的特性进行微调。
        "learning_rate_initial_imp": 0.05, # [浮点数] 初始重要性模型的学习率。
        "num_leaves_initial_imp": 15,                   # [整数] 初始重要性模型的每棵树最大叶子数。
        "max_depth_initial_imp": 5,                     # [整数] 初始重要性模型的每棵树最大深度。
        "reg_alpha_initial_imp": 5.0,                   # [浮点数] 初始重要性模型的L1正则化系数。
        "reg_lambda_initial_imp": 5.0,                  # [浮点数] 初始重要性模型的L2正则化系数。
        "colsample_bytree_initial_imp": 0.7132921738860881, # [浮点数] 初始重要性模型的列采样率。
        "subsample_initial_imp": 0.7754745906891941,    # [浮点数] 初始重要性模型的行采样率。
        "min_child_samples_initial_imp": 30,            # [整数] 初始重要性模型一个叶子节点上所需的最小数据量。
        "metric_initial_imp": 'binary_logloss',         # [字符串] 初始重要性模型训练时使用的评估指标。

        # --- Optuna 超参数优化 ---
        "optuna_enable": False,                         # [布尔值] 是否启用Optuna进行超参数优化。
        "optuna_n_trials": 100,                         # [整数] Optuna运行时尝试的试验次数。
        "optuna_timeout": None,                         # [整数 或 None] Optuna运行的最大秒数。如果为None，则不限制时间，直到达到 `optuna_n_trials`。
        "optuna_metric": "average_precision",           # [字符串] Optuna优化过程的目标指标，例如 "f1", "roc_auc", "accuracy", "average_precision"。
        "optuna_direction": "maximize",                 # [字符串] Optuna优化目标指标的方向: "maximize" (最大化) 或 "minimize" (最小化)。
        "optuna_cv_folds": 3,                           # [整数] Optuna在评估每个试验参数集时，内部进行交叉验证的折数。
        "optuna_trial_n_estimators_max": 1500,          # [整数] Optuna单次试验中，LightGBM模型允许训练的最大树数量 (通常配合早停)。
        "optuna_trial_early_stopping_rounds": 50,       # [整数] Optuna单次试验中，LightGBM训练的早停轮数。
        "optuna_trial_eval_metric": "binary_logloss",   # [字符串] Optuna单次试验中，LightGBM早停所依据的评估指标。

        # --- LightGBM 模型固定/回退参数 ---
        # 这些参数在 Optuna 未启用或优化失败时，作为模型的默认参数。
        # 如果Optuna成功，它找到的最佳参数会覆盖这里的对应项。
        "objective": "binary",                          # [字符串] LightGBM学习任务目标：'binary' 表示二分类。
        "metric": "binary_logloss",                     # [字符串 或 列表] 模型训练过程中的评估指标，也用于早停。例如 'binary_logloss', 'auc', 'average_precision'。
        "class_weight": {0: 1, 1: 1.5}, # <--- 添加或确保这行存在
        "boosting_type": "gbdt",                        # [字符串] LightGBM的提升类型，常用 'gbdt', 'dart', 'goss'。
        "random_state": 42,                             # [整数] 随机种子，用于确保模型训练的可复现性。
        "n_estimators": 8000,                           # [整数] 最终模型训练时允许的最大树数量 (通常配合早停)。
        "early_stopping_rounds": 250,                   # [整数] 最终模型训练时的早停轮数。如果在这么多轮内验证集指标没有改善，则停止训练。
        "verbose": 1,                                   # [整数] LightGBM训练日志的详细程度: -1 (静默), 0 (仅警告), 1 (信息), >1 (每N轮打印)。
        "verbosity": 1,                                 # [整数] 与 verbose 类似，用于控制LightGBM输出信息的级别 (通常设置一个即可，LGBMClassifier优先用verbosity)。
        "ensemble_runs": 1,                             # [整数] （已弃用或用途变更）原意可能是多次独立运行取平均，现在主要通过 ensemble_cv_folds 实现集成。
        "ensemble_cv_folds": 5,                         # [整数] 在最终模型训练时，使用多少折 TimeSeriesSplit 进行交叉验证式的集成训练。如果为1，则不进行CV集成，只训练一个单模型。

        # 以下为LightGBM的核心超参数，会被Optuna优化 (如果启用)
        "learning_rate": 0.008,           # [浮点数] 学习率。
        "num_leaves": 22,                               # [整数] 每棵树的最大叶子节点数。
        "max_depth": 6,                                 # [整数] 每棵树的最大深度。-1表示不限制。
        "reg_alpha": 7,                               # [浮点数] L1正则化系数。
        "reg_lambda": 7,                              # [浮点数] L2正则化系数。
        "colsample_bytree":  0.6,          # [浮点数, 0-1] 每棵树的列采样（特征）比例。
        "subsample":  0.7,                # [浮点数, 0-1] 每棵树的行采样（数据）比例。需要 boosting_type 为 'gbdt' 或 'dart'。
        "min_child_samples": 40,                        # [整数] 一个叶子节点上所需的最小数据量。
        "subsample_freq": 1,                            # [整数] 行采样的频率。0表示不进行行采样。
        "bagging_fraction": 0.8,            # 新增: 行采样比例 (等同于subsample)，明确写出
        "bagging_freq": 1,                  # 新增: 每迭代1次进行1次行采样
        "feature_fraction": 0.7,            # 新增: 列采样比例 (等同于colsample_bytree)，明确写出



        # --- SMOTE过采样配置 ---
        "smote_enable": True,                            # [布尔值] 是否为此目标启用SMOTE过采样
        "smote_k_neighbors": 5,                          # [整数] SMOTE的k_neighbors参数，会根据少数类样本数量自动调整
        "smote_min_samples_threshold": 5,                # [整数] 少数类样本的最小数量阈值，低于此值时跳过SMOTE
        "smote_random_state": 42,                        # [整数] SMOTE的随机种子

        # --- 最优阈值寻优配置 ---
        "threshold_optimization_enable": True,          # [布尔值] 是否为此目标启用最优阈值寻优
        "threshold_optimization_method": "f1",  # [字符串] 阈值优化方法:
                                                        # "f1": 最大化F1分数
                                                        # "precision_recall": 基于精确率-召回率曲线的最优点
                                                        # "youden": Youden指数 (敏感性 + 特异性 - 1)
                                                        # "balanced": 平衡精确率和召回率
                                                        # "precision_constrained_recall": 在精确率约束下最大化召回率
        "threshold_save_to_metadata": True,             # [布尔值] 是否将最优阈值保存到模型元数据
        "threshold_default_value": 0.5,                 # [浮点数] 默认决策阈值
        "threshold_use_independent_validation": False,   # [布尔值] 是否使用独立验证集进行阈值优化
        "threshold_independent_val_ratio": 0.15,        # [浮点数] 独立验证集比例

        # --- 精确率约束配置 ---
        "threshold_min_precision": 0.62,                # [浮点数] 最小精确率约束 (仅在method="precision_constrained_recall"时使用)
                                                        # 当使用精确率约束方法时，只考虑精确率 >= 此值的阈值
                                                        # 在满足约束的阈值中选择召回率最高的
        "threshold_precision_constraint_fallback": True, # [布尔值] 当没有阈值满足精确率约束时，是否使用最接近约束的阈值

        # --- 集成模型预测阈值配置 ---
        "ensemble_prediction_threshold": 0.6,        # [浮点数] 集成模型预测阈值，用于将概率预测转换为二分类决策
                                                        # 当集成模型输出的"明确上涨"概率超过此阈值时，产生UP信号
                                                        # 此阈值独立于训练时的最优阈值，专门用于实时预测决策
                                                        # 影响范围：实时预测、回测、信号生成
        "ensemble_prediction_threshold_override": True, # [布尔值] 是否在实时预测时使用ensemble_prediction_threshold覆盖训练时的最优阈值
                                                        # True: 使用ensemble_prediction_threshold作为预测阈值
                                                        # False: 使用训练时优化得到的最优阈值

        # --- 概率校准 ---
        "enable_probability_calibration": True,         # [布尔值] 是否在模型训练完成后，对模型的输出概率进行校准 (例如使用Platt Scaling或Isotonic Regression)。
        "calibration_brier_improvement_threshold": 0.0001, # [浮点数] 校准后的Brier Score需要比原始模型低至少这么多，才认为校准是有效的并采用校准后的模型。

        # --- SHAP 分析 ---
        "enable_shap_analysis": True,                  # [布尔值] 是否在模型训练完成后，进行全局SHAP分析以解释特征重要性，并保存SHAP解释器。
        "enable_shap_for_live_prediction": False,       # [布尔值] 是否在实时预测时计算每个预测的SHAP值。这通常性能开销较大，主要用于调试或深入分析。

        # --- 信号过滤逻辑 (方案一：关闭基础模型过滤，将信息作为特征喂给元模型) ---
        "enable_trend_detection": False,                # [布尔值] 关闭基础模型的趋势检测过滤，让元模型处理趋势信息。
            "trend_detection_timeframe": "30m",         # [字符串] 用于判断主要趋势的K线时间框架 (应等于或高于模型本身的 'interval')。
            "trend_indicator_type": 'adx',              # [字符串] 趋势判断使用的指标类型: 'adx' (Average Directional Index) 或 'ema_cross' (EMA均线交叉)。
            "trend_adx_period": 14,                     # [整数] 如果使用ADX，ADX的计算周期。
            "trend_adx_strength_threshold": 30,         # [整数] ADX值高于此阈值，认为趋势强劲。
            "trend_adx_threshold": 20,                  # [整数] ADX值高于此阈值，认为存在趋势（无论强弱）。
            "trend_ema_short_period": 20, "trend_ema_long_period": 50, # [整数] 如果使用EMA交叉，短期和长期EMA的周期。
            "trend_filter_strategy": 'filter_only',     # [字符串] 趋势过滤策略:
                                                        #   'filter_only': 如果信号与强趋势相反，则过滤掉信号。
                                                        #   'chase_trend': 如果当前无信号但存在强趋势，且模型对顺趋势方向有一定信心，则产生一个“追逐趋势”的信号。
                                                        #   'none': 不使用趋势过滤。
            "trend_chase_confidence_boost": 0.05,       # [浮点数, 0-1] 对于 'chase_trend' 策略，当追逐趋势时，对顺趋势方向概率的额外置信度提升要求。

        "enable_volatility_filter": False,              # [布尔值] 关闭基础模型的波动率过滤，让元模型处理波动率信息。
            "volatility_filter_timeframe": "30m",       # [字符串] 用于判断波动率的K线时间框架。
            "volatility_atr_period": 14,                # [整数] 计算波动率（基于ATR）的周期。
            "volatility_min_atr_percent": 0.1,          # [浮点数] ATR占收盘价的最小百分比。低于此值认为市场波动过低，可能过滤信号。
            "volatility_max_atr_percent": 1.2,          # [浮点数] ATR占收盘价的最大百分比。高于此值认为市场波动过高，可能过滤信号。

        "enable_dynamic_threshold": True,               # [布尔值] 是否根据当前趋势和波动率动态调整发出信号所需的概率阈值 (`signal_threshold`)。
            "dynamic_threshold_base": 0.4,              # [浮点数, 0-1] 动态阈值的基础值 (通常等于或接近静态的 `signal_threshold`)。
            "dynamic_threshold_trend_adjust": 0.05,     # [浮点数] 当信号与强趋势一致时，阈值降低的幅度；当信号与强趋势相反时，阈值增加的幅度。
            "dynamic_threshold_volatility_adjust": 0.03,# [浮点数] 当市场波动率过高或过低时，阈值增加的幅度 (使得信号更难触发)。

        # --- OOF参数 (如果计划让每个基础模型可以有不同的OOF生成策略，否则这些应为全局) ---
        # 注意：这些参数与顶层的 META_MODEL_OOF_... 参数用途相同，但在这里是针对单个基础模型。
        # 如果所有基础模型的OOF生成策略一致，则这些是冗余的，应使用全局参数。
        # 如果希望每个基础模型在OOF生成时有特定配置，则这些参数有效。
        "meta_model_oof_cv_folds": 5,                   # [整数] (同全局) 生成OOF预测时交叉验证的折数。
        "meta_model_oof_n_estimators_large": 3000,      # [整数] (同全局) OOF每折LGBM树数量。
        "meta_model_oof_early_stopping_rounds": 100,    # [整数] (同全局) OOF每折LGBM早停轮数。
        "meta_model_oof_early_stop_eval_ratio": 0.15,   # [浮点数] (同全局) OOF早停验证集比例。
        "meta_model_oof_min_samples_for_eval": 50,      # [整数] (同全局) OOF早停验证集最少样本。
    },

    # ==============================================================================
    # === 配置 BTC_15m_DOWN 模型 (专注于预测下跌信号) ===
    # ==============================================================================
    {
        # --- 目标基本信息 ---
        "name": "BTC_15m_DOWN",                         # [字符串] 目标唯一名称。
        "interval": "15m",                              # [字符串] K线周期。
        "symbol": "BTCUSDT",                            # [字符串] 交易对。
        "prediction_periods": [2],                      # [列表, 整数] 预测未来 2 * 15分钟 = 30分钟后的方向。
        "prediction_minutes_display": 30,               # [整数] GUI显示时效。
        "model_save_dir": "trained_models_btc_15m_down",# [字符串] 此DOWN模型的独立保存目录。
        "target_variable_type": "DOWN_ONLY",            # [字符串] **关键配置**: 此模型专门用于预测“明确下跌”。目标变量1为跌，0为非明确下跌。
        "drop_neutral_targets": False,                  # [布尔值] 通常为False，因为中性已归为0类。
        "device_type": 'gpu',                           # [字符串] 训练设备。
        "prediction_trigger_type": "kline_close",       # [字符串] 主要由K线关闭触发。
        # "apscheduler_job_enabled": True,              # [布尔值, 可选] 是否启用APScheduler。
        # "apscheduler_trigger_type": "cron",           # [字符串, 可选] APScheduler触发类型。
        # "apscheduler_cron_config": {"minute": "*/15"},# [字典, 可选] Cron配置，例如每15分钟。

        # --- 特征工程参数 ---
        # 🔧 Optimized Switches (优化开关):
        "enable_price_change": True,                    # [布尔值] 是否启用价格变动百分比特征。
        "enable_volume": True,                          # [布尔值] 是否启用成交量相关特征。
        "enable_candle": True,                          # [布尔值] 是否启用基于K线形状的特征。
        "enable_ta": True,                              # [布尔值] 是否启用基于pandas-ta库计算的技术分析指标。
        "enable_time": True,                            # [布尔值] 是否启用基于时间戳的特征。
        "enable_fund_flow": True,                       # [布尔值] 是否启用资金流相关特征。
        "enable_mtfa": True,                            # [布尔值] 是否启用多时间框架分析 (MTFA) 特征。
        "enable_pattern_recognition": True,             # [布尔值] 是否启用高级K线形态识别。
        "enable_trend_slope": True,                     # [布尔值] 是否启用趋势斜率分析特征。

        # ⚙️ Optimized Parameters (优化参数):
        "volume_avg_period": 21,                         # [整数] 成交量移动平均周期。
        "atr_period": 7,                               # [整数] Average True Range (ATR) 的周期。
        "rsi_period": 22,                               # [整数] Relative Strength Index (RSI) 的周期。
        "willr_period": 22,                             # [整数] Williams %R 周期参数 (匹配训练模型)
        "cci_period": 22,                               # [整数] CCI 周期参数 (匹配训练模型)
        "macd_fast": 15,                                # [整数] MACD快线EMA周期。
        "macd_slow": 27,                                # [整数] MACD慢线EMA周期。
        "macd_sign": 15,                                # [整数] MACD信号线EMA周期。
        "stoch_k": 8,                                  # [整数] Stochastic %K周期。
        "stoch_d": 5,                                   # [整数] Stochastic %D周期。
        "stoch_smooth_k": 5,                            # [整数] Stochastic %K平滑周期。
        "fund_flow_ratio_smoothing_period": 8,         # [整数] 资金流指标平滑处理周期 (匹配训练模型)。
        "target_threshold": 0.0018,                     # [浮点数] 目标变量阈值。
        "hma_period": 21,                               # [整数] Hull Moving Average (HMA) 周期。
        "kc_period": 24,                                # [整数] Keltner Channel EMA中线周期。
        "kc_atr_period": 17,                            # [整数] Keltner Channel ATR计算周期。
        "kc_multiplier": 2.431932682355995,                      # [浮点数] Keltner Channel ATR倍数。
        "signal_threshold": 0.6309671569917381,                        # [浮点数] 模型预测信号阈值。
        "trend_adx_period": 11,                         # [整数] ADX趋势指标计算周期。
        "trend_adx_threshold": 32,                      # [整数] ADX趋势强度阈值。
        "trend_ema_short_period": 19,                   # [整数] 趋势分析短期EMA周期。
        "trend_ema_long_period": 35,                    # [整数] 趋势分析长期EMA周期。
        "trend_slope_period_1": 3,                      # [整数] 第一个趋势斜率计算周期。
        "trend_slope_period_2": 22,                     # [整数] 第二个趋势斜率计算周期。
        # 🎯 新增：DOWN模型专用EMA距离特征参数
        "ema_short_period_down": 10,                    # [整数] DOWN模型短期EMA周期，用于计算均线距离特征。
        "ema_long_period_down": 30,                     # [整数] DOWN模型长期EMA周期，用于计算均线距离特征。
        # 🎯 新增：DOWN模型专用布林带突破强度特征参数
        "bb_period": 20,                                # [整数] 布林带计算周期。
        "bb_std": 2.0,                                  # [浮点数] 布林带标准差倍数。
        # 🎯 新增：时间框架敏感度特征参数
        "enable_timeframe_sensitivity": True,           # [布尔值] 是否启用时间框架敏感度特征。
        "tf_sensitivity_reference_timeframe": "4h",     # [字符串] 参考时间框架（用于对比）。
        "tf_sensitivity_features": ["rsi", "close_pos_in_candle", "macd", "volume_ratio"],  # [列表] 要计算对比的特征类型。
        "doji_threshold": 0.1453139927466848,                     # [浮点数] 十字星形态阈值。
        "hammer_body_ratio": 0.38937696296971414,                  # [浮点数] 锤子线实体比例阈值。
        "hammer_shadow_ratio": 2.2601513266757376,                # [浮点数] 锤子线影线比例阈值。
        "marubozu_threshold": 0.8741915362541589,                     # [浮点数] 光头光脚线阈值。
        "spinning_top_threshold": 0.6376683909810659,                  # [浮点数] 陀螺线阈值。
        "engulfing_body_multiplier": 1.05,          # [浮点数] 吞没形态倍数阈值。
        "morning_evening_star_body_ratio": 0.09062130825046674,    # [浮点数] 启明星/黄昏星比例阈值。
        "doji_threshold_batac": 0.11355368203773268,               # [浮点数] 多K线十字星阈值。

        # 📊 Additional Configuration (附加配置):
        "price_change_periods": [1, 2, 3, 5, 10],       # [列表] 价格变动计算周期。
        "cci_constant": 0.002404,                       # [浮点数] CCI计算常数 (匹配DOWN模型)。
        "enable_time_trigonometric": True,              # [布尔值] 是否启用时间三角函数编码。
        "enable_adx_trend_features": True,              # [布尔值] 是否启用ADX趋势特征。
        "enable_ema_trend_features": True,              # [布尔值] 是否启用EMA交叉趋势特征。
        "enable_ta_derived_features": True,             # [布尔值] 是否启用技术指标衍生特征。
        "mtfa_timeframes": ['30m','1h', '4h'],          # [列表] MTFA时间框架。
        "mtfa_feature_lookback_periods": 200,           # [整数] MTFA特征回看周期。
        "mtfa_specific_lookbacks": {},                  # [字典] MTFA特定回看周期。
        "mtfa_min_bars_to_fetch": 50,                   # [整数] MTFA最小K线数量。
        "mtfa_min_bars_for_calc": 50,                   # [整数] MTFA计算最小K线数量。
        "mtfa_fetch_buffer": 10,                        # [整数] MTFA数据获取缓冲区。

        # --- 高级数据处理配置 ---
        "enable_intelligent_nan_processing": True,      # [布尔值] 是否启用智能NaN/Inf处理，根据特征类型选择合适的默认值。
        "enable_safe_fill_nans": True,                  # [布尔值] 是否启用安全的NaN填充方法，仅使用历史数据进行填充。
        "min_historical_bars_for_prediction": 100,      # [整数] 实时预测时所需的最小历史K线数量。
        "enable_advanced_feature_validation": True,     # [布尔值] 是否启用高级特征验证和错误处理。

        # --- 目标变量定义 和 预测信号阈值 (可与UP模型不同，针对DOWN预测调整) ---
        "target_threshold": 0.0018,      # [浮点数] 定义“明确下跌”所需的最小价格变动百分比 (例如，价格下跌超过0.2%)。
        "signal_threshold": 0.7,        # [浮点数, 0-1] 模型输出的“明确下跌”概率需要高于此阈值，才初步认为是一个有效的看空信号。

        # --- RFE (Recursive Feature Elimination) 配置 ---
        "rfe_enable": False,                            # [布尔值] 是否启用RFE递归特征消除来进行特征选择。
         "rfe_cv_folds": 3,                              # [整数, 可选] 如果启用RFE，RFE内部交叉验证的折数。
         "rfe_step": 1,                                  # [整数或浮点数, 可选] RFE每次迭代移除特征的数量或百分比。
         "rfe_scoring": "f1",                      # [字符串, 可选] RFE中评估特征子集好坏所用的评分指标。
         "rfe_estimator_n_estimators": 100,              # [整数, 可选] RFE内部使用的LightGBM评估器的树数量。
         "rfe_estimator_learning_rate": 0.1,             # [浮点数, 可选] RFE内部LightGBM评估器的学习率。
         "rfe_estimator_num_leaves": 31,                 # [整数, 可选] RFE内部LightGBM评估器的叶子数。
         "rfe_min_features_to_select": 10,               # [整数, 可选] RFE最终选择的最小特征数量。

        # --- 特征选择：基于初始模型重要性进行筛选 ---
        "importance_thresholding_enable": True,         # [布尔值] 是否在RFE之后（或直接在全部特征上）再进行一次基于LightGBM初始重要性的特征筛选。
        "importance_threshold_value": 0,               # [整数] 如果 `importance_top_n_features` 未设置或为None，则移除重要性绝对值低于此阈值的特征。
        "importance_top_n_features": 80,                # [整数 或 None] 如果设置了此值，则优先选择重要性最高的Top N个特征。如果为None，则使用 `importance_threshold_value`。
        "importance_model_n_estimators": 300,           # [整数] 用于获取初始特征重要性的临时LightGBM模型的树数量。
        # 以下为用于获取初始重要性的LGBM模型的超参数，可以根据DOWN模型的特性进行微调。
        "learning_rate_initial_imp": 0.05, # [浮点数] 初始重要性模型的学习率。
        "num_leaves_initial_imp": 15,                   # [整数] 初始重要性模型的每棵树最大叶子数。
        "max_depth_initial_imp": 5,                     # [整数] 初始重要性模型的每棵树最大深度。
        "reg_alpha_initial_imp": 5.0,                   # [浮点数] 初始重要性模型的L1正则化系数。
        "reg_lambda_initial_imp": 5.0,                  # [浮点数] 初始重要性模型的L2正则化系数。
        "colsample_bytree_initial_imp": 0.7132921738860881, # [浮点数] 初始重要性模型的列采样率。
        "subsample_initial_imp": 0.7754745906891941,    # [浮点数] 初始重要性模型的行采样率。
        "min_child_samples_initial_imp": 30,            # [整数] 初始重要性模型一个叶子节点上所需的最小数据量。
        "metric_initial_imp": 'binary_logloss',         # [字符串] 初始重要性模型训练时使用的评估指标。

        # --- Optuna 超参数优化 ---
        "optuna_enable": False,                         # [布尔值] 是否启用Optuna进行超参数优化。
        "optuna_n_trials": 150,                         # [整数] Optuna运行时尝试的试验次数。
        "optuna_timeout": 7200,                         # [整数 或 None] Optuna运行的最大秒数。如果为None，则不限制时间，直到达到 `optuna_n_trials`。
        "optuna_metric": "average_precision",           # [字符串] Optuna优化过程的目标指标，例如 "f1", "roc_auc", "accuracy", "average_precision"。
        "optuna_direction": "maximize",                 # [字符串] Optuna优化目标指标的方向: "maximize" (最大化) 或 "minimize" (最小化)。
        "optuna_cv_folds": 3,                           # [整数] Optuna在评估每个试验参数集时，内部进行交叉验证的折数。
        "optuna_trial_n_estimators_max": 1500,          # [整数] Optuna单次试验中，LightGBM模型允许训练的最大树数量 (通常配合早停)。
        "optuna_trial_early_stopping_rounds": 50,       # [整数] Optuna单次试验中，LightGBM训练的早停轮数。
        "optuna_trial_eval_metric": "binary_logloss",   # [字符串] Optuna单次试验中，LightGBM早停所依据的评估指标。

        # --- LightGBM 模型固定/回退参数 ---
        # 这些参数在 Optuna 未启用或优化失败时，作为模型的默认参数。
        # 如果Optuna成功，它找到的最佳参数会覆盖这里的对应项。
        "objective": "binary",                          # [字符串] LightGBM学习任务目标：'binary' 表示二分类。
        "metric": "binary_logloss",                     # [字符串 或 列表] 模型训练过程中的评估指标，也用于早停。例如 'binary_logloss', 'auc', 'average_precision'。
        "class_weight": {0: 1, 1: 1.5}, # <--- 添加或确保这行存在
        "boosting_type": "gbdt",                        # [字符串] LightGBM的提升类型，常用 'gbdt', 'dart', 'goss'。
        "random_state": 42,                             # [整数] 随机种子，用于确保模型训练的可复现性。
        "n_estimators": 8000,                           # [整数] 最终模型训练时允许的最大树数量 (通常配合早停)。
        "early_stopping_rounds": 250,                   # [整数] 最终模型训练时的早停轮数。如果在这么多轮内验证集指标没有改善，则停止训练。
        "verbose": 1,                                   # [整数] LightGBM训练日志的详细程度: -1 (静默), 0 (仅警告), 1 (信息), >1 (每N轮打印)。
        "verbosity": 1,                                 # [整数] 与 verbose 类似，用于控制LightGBM输出信息的级别 (通常设置一个即可，LGBMClassifier优先用verbosity)。
        "ensemble_runs": 1,                             # [整数] （已弃用或用途变更）原意可能是多次独立运行取平均，现在主要通过 ensemble_cv_folds 实现集成。
        "ensemble_cv_folds": 5,                         # [整数] 在最终模型训练时，使用多少折 TimeSeriesSplit 进行交叉验证式的集成训练。如果为1，则不进行CV集成，只训练一个单模型。

        # 以下为LightGBM的核心超参数，会被Optuna优化 (如果启用)
        "learning_rate": 0.0138,           # [浮点数] 学习率。
        "num_leaves": 22,                               # [整数] 每棵树的最大叶子节点数。
        "max_depth": 5,                                 # [整数] 每棵树的最大深度。-1表示不限制。
        "reg_alpha": 7.1708,                               # [浮点数] L1正则化系数。
        "reg_lambda": 4.8002,                              # [浮点数] L2正则化系数。
        "colsample_bytree":  0.5422,          # [浮点数, 0-1] 每棵树的列采样（特征）比例。
        "subsample":  0.5347,                # [浮点数, 0-1] 每棵树的行采样（数据）比例。需要 boosting_type 为 'gbdt' 或 'dart'。
        "min_child_samples": 74,                        # [整数] 一个叶子节点上所需的最小数据量。
        "subsample_freq": 1,                            # [整数] 行采样的频率。0表示不进行行采样。
        "bagging_fraction": 0.8,            # 新增: 行采样比例 (等同于subsample)，明确写出
        "bagging_freq": 1,                  # 新增: 每迭代1次进行1次行采样
        "feature_fraction": 0.7,            # 新增: 列采样比例 (等同于colsample_bytree)，明确写出



        # --- SMOTE过采样配置 ---
        "smote_enable": True,                            # [布尔值] 是否为此目标启用SMOTE过采样
        "smote_k_neighbors": 5,                          # [整数] SMOTE的k_neighbors参数，会根据少数类样本数量自动调整
        "smote_min_samples_threshold": 5,                # [整数] 少数类样本的最小数量阈值，低于此值时跳过SMOTE
        "smote_random_state": 42,                        # [整数] SMOTE的随机种子

        # --- 最优阈值寻优配置 ---
        "threshold_optimization_enable": True,          # [布尔值] 是否为此目标启用最优阈值寻优
        "threshold_optimization_method": "f1",  # [字符串] 阈值优化方法:
                                                        # "f1": 最大化F1分数
                                                        # "precision_recall": 基于精确率-召回率曲线的最优点
                                                        # "youden": Youden指数 (敏感性 + 特异性 - 1)
                                                        # "balanced": 平衡精确率和召回率
                                                        # "precision_constrained_recall": 在精确率约束下最大化召回率
        "threshold_save_to_metadata": True,             # [布尔值] 是否将最优阈值保存到模型元数据
        "threshold_default_value": 0.5,                 # [浮点数] 默认决策阈值
        "threshold_use_independent_validation": False,   # [布尔值] 是否使用独立验证集进行阈值优化
        "threshold_independent_val_ratio": 0.15,        # [浮点数] 独立验证集比例

        # --- 精确率约束配置 ---
        "threshold_min_precision": 0.62,                # [浮点数] 最小精确率约束 (仅在method="precision_constrained_recall"时使用)
                                                        # 当使用精确率约束方法时，只考虑精确率 >= 此值的阈值
                                                        # 在满足约束的阈值中选择召回率最高的
        "threshold_precision_constraint_fallback": True, # [布尔值] 当没有阈值满足精确率约束时，是否使用最接近约束的阈值

        # --- 集成模型预测阈值配置 ---
        "ensemble_prediction_threshold": 0.6,        # [浮点数] 集成模型预测阈值，用于将概率预测转换为二分类决策
                                                        # 当集成模型输出的"明确上涨"概率超过此阈值时，产生UP信号
                                                        # 此阈值独立于训练时的最优阈值，专门用于实时预测决策
                                                        # 影响范围：实时预测、回测、信号生成
        "ensemble_prediction_threshold_override": True, # [布尔值] 是否在实时预测时使用ensemble_prediction_threshold覆盖训练时的最优阈值
                                                        # True: 使用ensemble_prediction_threshold作为预测阈值
                                                        # False: 使用训练时优化得到的最优阈值

        # --- 概率校准 (可独立配置是否对DOWN模型进行校准) ---
        "enable_probability_calibration": True,       # [布尔值] 是否对DOWN模型输出的概率进行校准。
        "calibration_brier_improvement_threshold": 0.0001, # [浮点数] 校准后Brier Score改善阈值。

        # --- SHAP 分析 (可独立配置是否为DOWN模型进行SHAP分析) ---
        "enable_shap_analysis": True,               # [布尔值] 是否为DOWN模型进行SHAP分析。
        "enable_shap_for_live_prediction": False,     # [布尔值] 实时预测DOWN信号时是否计算SHAP值 (通常保持False以优化性能)。

        # --- 信号过滤逻辑 (方案一：关闭基础模型过滤，将信息作为特征喂给元模型) ---
        "enable_trend_detection": False,              # [布尔值] 关闭DOWN基础模型的趋势检测过滤，让元模型处理趋势信息。
            "trend_detection_timeframe": "30m",       # [字符串] 判断趋势的K线周期。
            "trend_indicator_type": 'adx',            # [字符串] 趋势指标类型 ('adx' 或 'ema_cross')。
            "trend_adx_period": 14,                   # [整数] ADX周期。
            "trend_adx_strength_threshold": 30,       # [整数] ADX强趋势阈值。
            "trend_adx_threshold": 20,                # [整数] ADX趋势存在阈值。
            "trend_filter_strategy": 'filter_only',   # [字符串] 趋势过滤策略 ('filter_only', 'chase_trend', 'none')。
            "trend_chase_confidence_boost": 0.05,     # [浮点数] 'chase_trend'策略下的概率提升。
        "enable_volatility_filter": False,            # [布尔值] 关闭DOWN基础模型的波动率过滤，让元模型处理波动率信息。
            "volatility_filter_timeframe": "30m",     # [字符串] 判断波动率的K线周期。
            "volatility_atr_period": 14,              # [整数] ATR周期。
            "volatility_min_atr_percent": 0.1,        # [浮点数] 最小ATR百分比 (波动过低)。
            "volatility_max_atr_percent": 1.2,        # [浮点数] 最大ATR百分比 (波动过高)。
        "enable_dynamic_threshold": True,             # [布尔值] 是否启用动态信号阈值调整。
            "dynamic_threshold_base": 0.7,           # [浮点数, 0-1] DOWN模型的基础信号概率阈值，可以与UP模型不同。
            "dynamic_threshold_trend_adjust": 0.07,   # [浮点数] 根据趋势调整阈值的幅度。
            "dynamic_threshold_volatility_adjust": 0.03,# [浮点数] 根据波动率调整阈值的幅度。

        # --- OOF参数 (如果需要为DOWN模型独立配置OOF生成策略，否则这些应为全局参数) ---
        # 这些参数与全局的 META_MODEL_OOF_... 参数用途相同。
        # 如果所有基础模型的OOF生成策略一致，则这些是冗余的，应使用全局参数。
        # 如果希望DOWN模型在OOF生成时有特定配置，则这些参数有效。
        "meta_model_oof_cv_folds": 5,                   # [整数] 生成OOF预测时交叉验证的折数。
        "meta_model_oof_n_estimators_large": 3000,      # [整数] OOF每折LGBM树数量。
        "meta_model_oof_early_stopping_rounds": 100,    # [整数] OOF每折LGBM早停轮数。
        "meta_model_oof_early_stop_eval_ratio": 0.15,   # [浮点数] OOF早停验证集比例。
        "meta_model_oof_min_samples_for_eval": 50,      # [整数] OOF早停验证集最少样本。
    },


    { # 新增：元模型的GUI显示条目 和 APScheduler 触发配置
        "name": META_MODEL_GUI_DISPLAY_NAME,          # [字符串] 在GUI中标识元模型预测结果的名称，应与全局定义一致。
        "symbol": "BTCUSDT",                          # [字符串] 元模型关联的主要交易对
        "interval": "Meta-Analysis",                  # [字符串] GUI中显示的“周期”，表明这是综合分析。
        "prediction_periods": [1],                    # [列表] 修复：使用[1]而不是[0]，避免验证错误
        "prediction_minutes_display": "实时决策",     # [字符串] GUI中显示的预测时效。
        "model_save_dir": "meta_model_display",       # [字符串] 元模型显示条目的目录（虽然不实际保存模型）
        "target_variable_type": "META_MODEL_DISPLAY", # [字符串] 特殊类型，标记此条目仅用于在GUI中显示元模型结果，不参与独立训练。
        "prediction_trigger_type": "apscheduler_driven", # [字符串] 表明此“目标”的“预测”（即元模型决策的更新）主要由APScheduler任务驱动。

        # --- APScheduler 作业配置 (用于定时触发元模型预测更新) ---
        "apscheduler_job_enabled": True,                # [布尔值] 是否为这个元模型显示目标启用APScheduler作业。
        "apscheduler_trigger_type": "cron",             # [字符串] 触发类型: 'cron', 'interval', 'date'。
        "apscheduler_cron_config": {"minute": "*"}   # [字典或字符串] Cron配置。例如:
                                                        #   {"minute": "*/15"} 表示每15分钟在00, 15, 30, 45分时触发。
                                                        #   {"minute": "5,35"} 表示每小时的第5分钟和第35分钟触发。
                                                        #   {"minute": "0"} 表示每小时的第0分钟触发。
                                                        #   如果用 "interval",  例如: {"minutes": 15} 表示每隔15分钟触发一次。
    }
]

# --- 默认配置字典 ---
# 包含所有可能参数的默认配置，确保即使目标配置中遗漏了某个参数，也能回退到合理的默认值
DEFAULT_TARGET_CONFIG = {
    # 基本信息
    'name': 'default_target',  # 添加缺少的 name 字段
    'symbol': SYMBOL,
    'scaler_type': SCALER_TYPE,
    'device_type': 'cpu',
    'interval': '15m',
    'prediction_periods': [1],
    'prediction_minutes_display': 15,
    'model_save_dir': 'trained_models_default',
    'target_variable_type': 'UP_ONLY',
    'drop_neutral_targets': False,
    'prediction_trigger_type': 'kline_close',

    # 特征工程开关
    'enable_price_change': True,
    'enable_volume': True,
    'enable_candle': True,
    'enable_ta': True,
    'enable_time': True,
    'enable_fund_flow': True,
    'enable_mtfa': True,
    'enable_pattern_recognition': False,
    'enable_trend_slope': True,

    # 特征工程参数
    'volume_avg_period': 20,
    'atr_period': 14,
    'rsi_period': 14,
    'willr_period': 14,                    # [整数] Williams %R 周期参数
    'cci_period': 14,                      # [整数] CCI 周期参数 (独立于RSI)
    'macd_fast': 12,
    'macd_slow': 26,
    'macd_sign': 9,
    'stoch_k': 14,
    'stoch_d': 3,
    'stoch_smooth_k': 3,
    'fund_flow_ratio_smoothing_period': 5,     # [整数] 资金流比率平滑周期 (统一为5以匹配期望特征名)
    'target_threshold': 0.001,
    'hma_period': 21,
    'kc_period': 20,
    'kc_atr_period': 10,
    'kc_multiplier': 2.0,
    'signal_threshold': 0.5,
    'trend_adx_period': 14,
    'trend_adx_threshold': 25,
    'trend_adx_strength_threshold': 30,  # 添加缺少的字段
    'trend_ema_short_period': 20,
    'trend_ema_long_period': 50,
    'trend_slope_period_1': 5,
    'trend_slope_period_2': 10,
    'doji_threshold': 0.1,
    'hammer_body_ratio': 0.3,
    'hammer_shadow_ratio': 2.0,
    'marubozu_threshold': 0.9,
    'spinning_top_threshold': 0.5,
    'engulfing_body_multiplier': 1.2,
    'morning_evening_star_body_ratio': 0.3,
    'doji_threshold_batac': 0.1,

    # 高级配置
    'price_change_periods': [1, 2, 3, 5, 10],
    'cci_constant': 0.015,
    'enable_time_trigonometric': True,
    'enable_adx_trend_features': True,
    'enable_ema_trend_features': True,
    'enable_ta_derived_features': True,
    'mtfa_timeframes': ['30m', '1h', '4h'],
    'mtfa_feature_lookback_periods': 200,
    'mtfa_specific_lookbacks': {},
    'mtfa_min_bars_to_fetch': 50,
    'mtfa_min_bars_for_calc': 50,
    'mtfa_fetch_buffer': 10,

    # 数据处理配置
    'enable_intelligent_nan_processing': True,
    'enable_safe_fill_nans': True,
    'min_historical_bars_for_prediction': 100,
    'enable_advanced_feature_validation': True,

    # RFE配置
    'rfe_enable': False,
    'rfe_cv_folds': 3,
    'rfe_step': 1,
    'rfe_scoring': 'accuracy',
    'rfe_estimator_n_estimators': 100,
    'rfe_estimator_learning_rate': 0.1,
    'rfe_estimator_num_leaves': 31,
    'rfe_min_features_to_select': 10,

    # 特征选择配置
    'importance_thresholding_enable': False,
    'importance_threshold_value': 20,
    'importance_top_n_features': 50,
    'importance_model_n_estimators': 200,
    'learning_rate_initial_imp': 0.05,
    'num_leaves_initial_imp': 15,
    'max_depth_initial_imp': 5,
    'reg_alpha_initial_imp': 5.0,
    'reg_lambda_initial_imp': 5.0,
    'colsample_bytree_initial_imp': 0.7,
    'subsample_initial_imp': 0.7,
    'min_child_samples_initial_imp': 30,
    'metric_initial_imp': 'binary_logloss',

    # Optuna配置
    'optuna_enable': False,
    'optuna_n_trials': 100,
    'optuna_timeout': None,
    'optuna_metric': 'accuracy',
    'optuna_direction': 'maximize',
    'optuna_cv_folds': 3,
    'optuna_trial_n_estimators_max': 1500,
    'optuna_trial_early_stopping_rounds': 50,
    'optuna_trial_eval_metric': 'binary_logloss',

    # LightGBM参数
    'objective': 'binary',
    'metric': 'binary_logloss',
    'class_weight': None,
    'boosting_type': 'gbdt',
    'random_state': 42,
    'n_estimators': 1000,
    'early_stopping_rounds': 100,
    'verbose': -1,
    'verbosity': -1,
    'ensemble_runs': 1,
    'ensemble_cv_folds': 3,
    'learning_rate': 0.1,
    'num_leaves': 31,
    'max_depth': -1,
    'reg_alpha': 0.0,
    'reg_lambda': 0.0,
    'colsample_bytree': 1.0,
    'subsample': 1.0,
    'min_child_samples': 20,
    'subsample_freq': 0,

    # SMOTE配置
    'smote_enable': False,
    'smote_k_neighbors': 5,
    'smote_min_samples_threshold': 5,
    'smote_random_state': 42,

    # 阈值优化配置
    'threshold_optimization_enable': False,
    'threshold_optimization_method': 'f1',
    'threshold_save_to_metadata': True,
    'threshold_default_value': 0.5,
    'threshold_use_independent_validation': True,
    'threshold_independent_val_ratio': 0.15,
    'threshold_min_precision': 0.6,
    'threshold_precision_constraint_fallback': True,

    # 集成模型配置
    'ensemble_prediction_threshold': 0.5,
    'ensemble_prediction_threshold_override': False,

    # 概率校准配置
    'enable_probability_calibration': False,
    'calibration_brier_improvement_threshold': 0.001,

    # SHAP分析配置
    'enable_shap_analysis': False,
    'enable_shap_for_live_prediction': False,

    # 信号过滤配置
    'enable_trend_detection': False,
    'trend_detection_timeframe': '30m',
    'trend_indicator_type': 'adx',
    'trend_filter_strategy': 'filter_only',
    'trend_chase_confidence_boost': 0.05,
    'enable_volatility_filter': False,
    'volatility_filter_timeframe': '30m',
    'volatility_atr_period': 14,
    'volatility_min_atr_percent': 0.1,
    'volatility_max_atr_percent': 1.2,
    'enable_dynamic_threshold': False,
    'dynamic_threshold_base': 0.5,
    'dynamic_threshold_trend_adjust': 0.05,
    'dynamic_threshold_volatility_adjust': 0.03,

    # OOF参数
    'meta_model_oof_cv_folds': 5,
    'meta_model_oof_n_estimators_large': 3000,
    'meta_model_oof_early_stopping_rounds': 100,
    'meta_model_oof_early_stop_eval_ratio': 0.15,
    'meta_model_oof_min_samples_for_eval': 50,
}


# --- 类型安全的辅助函数：获取目标配置 ---
@overload
def get_target_config(target_name: str, *, validate_types: Literal[True]) -> TargetConfigDict:
    """类型安全版本，返回完整类型化的配置字典"""
    ...

@overload
def get_target_config(target_name: str, *, validate_types: Literal[False] = False) -> ConfigDict:
    """标准版本，返回通用配置字典"""
    ...

def get_target_config(
    target_name: str,
    *,
    validate_types: bool = False
) -> Union[TargetConfigDict, ConfigDict]:
    """
    根据目标名称从 PREDICTION_TARGETS 列表中获取完整的配置字典。
    支持配置模板继承、全局开关覆盖、细化错误处理，保持原始键名。

    Args:
        target_name: 要获取配置的目标的名称。必须是非空字符串。
        validate_types: 是否进行严格的类型验证。如果为True，返回类型化的配置字典。

    Returns:
        包含该目标所有配置项的字典。根据validate_types参数返回不同类型：
        - validate_types=True: 返回TargetConfigDict（类型安全）
        - validate_types=False: 返回ConfigDict（通用字典）

    Raises:
        ValueError: 如果在 PREDICTION_TARGETS 中找不到指定名称的目标配置，或目标名称为空。
        TypeError: 如果目标名称不是字符串类型。

    Example:
        >>> config = get_target_config("BTC_15m_UP")
        >>> typed_config = get_target_config("BTC_15m_UP", validate_types=True)
    """
    # 参数类型检查
    if not isinstance(target_name, str):
        raise TypeError(f"目标名称必须是字符串类型，当前类型: {type(target_name)}")

    if not target_name.strip():
        raise ValueError("目标名称不能为空")

    # 从 PREDICTION_TARGETS 列表中查找具有匹配名称的目标配置
    target_setting_from_list = next((t for t in PREDICTION_TARGETS if t.get('name') == target_name), None)

    if not target_setting_from_list:
        available_targets = [t.get('name', 'Unknown') for t in PREDICTION_TARGETS if isinstance(t, dict)]
        raise ValueError(f"在 PREDICTION_TARGETS 中未找到名为 '{target_name}' 的配置。"
                        f"可用的目标配置: {available_targets}")

    # 1. 构建配置层次结构：默认配置 -> 全局默认值 -> 模板配置 -> 目标特定配置
    final_config = _build_hierarchical_config(target_setting_from_list, target_name)

    # 2. 应用全局硬性开关覆盖
    _apply_global_hard_switches(final_config, target_name)

    # 3. 验证和格式化配置参数
    _validate_and_format_config(final_config, target_name)

    # 4. 处理时间间隔转换
    _process_interval_timedelta(final_config, target_name)

    # 5. 运行时类型验证（如果需要）
    if validate_types:
        _validate_runtime_types(final_config, target_name)

    return final_config


def _build_hierarchical_config(target_config: ConfigDict, target_name: str) -> ConfigDict:
    """
    构建层次化配置：默认配置 -> 全局默认值 -> 模板配置 -> 目标特定配置

    Args:
        target_config: 目标特定配置字典
        target_name: 目标名称，用于错误信息和模板选择

    Returns:
        合并后的配置字典

    Raises:
        TypeError: 如果输入参数类型不正确
    """
    # 运行时类型检查
    if not isinstance(target_config, dict):
        raise TypeError(f"target_config 必须是字典类型，当前类型: {type(target_config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 1. 从基础默认配置开始
    final_config = DEFAULT_TARGET_CONFIG.copy()

    # 2. 应用全局默认值
    for key, value in GLOBAL_DEFAULTS.items():
        final_config[key] = value

    # 3. 检查是否需要应用模板配置
    template_name = target_config.get('template', None)
    if template_name == 'base_model' or _is_base_model_target(target_name):
        # 应用基础模型模板
        for key, value in BASE_MODEL_TEMPLATE.items():
            final_config[key] = value

    # 4. 应用目标特定配置（具有最高优先级）
    for key, value in target_config.items():
        final_config[key] = value

    return final_config


def _is_base_model_target(target_name: str) -> bool:
    """
    判断是否为基础模型目标（自动应用基础模型模板）

    Args:
        target_name: 目标名称，必须是非空字符串

    Returns:
        是否为基础模型目标

    Raises:
        TypeError: 如果target_name不是字符串类型
    """
    # 运行时类型检查
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 基础模型通常包含这些模式
    base_model_patterns = ['_UP', '_DOWN', 'BTC_', 'ETH_', 'USDT']
    meta_model_patterns = ['Meta', 'META', 'Signal']

    # 如果包含元模型模式，则不是基础模型
    if any(pattern in target_name for pattern in meta_model_patterns):
        return False

    # 如果包含基础模型模式，则是基础模型
    return any(pattern in target_name for pattern in base_model_patterns)


def _apply_global_hard_switches(config: ConfigDict, target_name: str) -> None:
    """
    应用全局硬性开关，这些开关会覆盖目标配置中的对应设置

    Args:
        config: 配置字典，将被就地修改
        target_name: 目标名称，用于错误信息

    Raises:
        TypeError: 如果输入参数类型不正确
    """
    # 运行时类型检查
    if not isinstance(config, dict):
        raise TypeError(f"config 必须是字典类型，当前类型: {type(config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 映射全局开关到目标配置键
    switch_mappings = {
        'smote_global_enable': 'smote_enable',
        'threshold_optimization_global_enable': 'threshold_optimization_enable',
        # 元模型相关开关只影响元模型目标
        'meta_model_training_enable': None,  # 这个在训练逻辑中处理
        'meta_model_prediction_enable': None,  # 这个在预测逻辑中处理
    }

    for global_switch, target_key in switch_mappings.items():
        if target_key and global_switch in GLOBAL_HARD_SWITCHES:
            global_value = GLOBAL_HARD_SWITCHES[global_switch]
            if not global_value:  # 如果全局开关为False，强制覆盖目标配置
                config[target_key] = False
                if config.get(target_key, True):  # 只在原来为True时打印警告
                    print(f"警告: 目标 '{target_name}' 的 {target_key} 被全局开关 {global_switch} 强制设置为 False")





def _validate_and_format_config(config: ConfigDict, target_name: str) -> None:
    """
    验证和格式化配置参数。

    Args:
        config: 配置字典，将被就地修改
        target_name: 目标名称，用于错误信息

    Raises:
        TypeError: 如果输入参数类型不正确
    """
    # 运行时类型检查
    if not isinstance(config, dict):
        raise TypeError(f"config 必须是字典类型，当前类型: {type(config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 设备类型验证和格式化
    if 'device_type' in config:
        device_type = str(config['device_type']).lower()
        if device_type not in ['cpu', 'gpu']:
            print(f"警告: 目标 '{target_name}' 的 device_type '{config['device_type']}' 无效，"
                  f"将使用默认值 'cpu'")
            device_type = 'cpu'
        config['device_type'] = device_type

    # 数值参数验证
    numeric_params = {
        'target_threshold': (0.0, 1.0),
        'signal_threshold': (0.0, 1.0),
        'learning_rate': (0.001, 1.0),
        'train_ratio': (0.1, 0.9),
        'validation_ratio': (0.05, 0.5),
    }

    for param, (min_val, max_val) in numeric_params.items():
        if param in config:
            try:
                value = float(config[param])
                if not (min_val <= value <= max_val):
                    print(f"警告: 目标 '{target_name}' 的 {param} 值 {value} 超出合理范围 "
                          f"[{min_val}, {max_val}]，将使用默认值")
                    # 使用默认值替换超出范围的值
                    config[param] = DEFAULT_TARGET_CONFIG.get(param, 0.5)
                else:
                    config[param] = value
            except (ValueError, TypeError):
                print(f"警告: 目标 '{target_name}' 的 {param} 值 '{config[param]}' 无法转换为数值，"
                      f"将使用默认值")
                config[param] = DEFAULT_TARGET_CONFIG.get(param, 0.5)


def _process_interval_timedelta(config: ConfigDict, target_name: str) -> None:
    """
    处理时间间隔字符串转换为 pandas.Timedelta 对象。

    Args:
        config: 配置字典，将被就地修改
        target_name: 目标名称，用于错误信息

    Raises:
        TypeError: 如果输入参数类型不正确
        ValueError: 如果时间间隔配置无效
    """
    # 运行时类型检查
    if not isinstance(config, dict):
        raise TypeError(f"config 必须是字典类型，当前类型: {type(config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    interval_str = config.get('interval')
    target_type = config.get('target_variable_type')

    # 如果是元模型的GUI显示条目，或者 'interval' 被明确设置为不适用
    if target_type == "META_MODEL_DISPLAY" or interval_str in ["N/A", "Meta-Analysis", None]:
        config['interval_timedelta'] = None
        return

    # 尝试转换时间间隔字符串
    if not interval_str:
        raise ValueError(f"目标 '{target_name}' 配置中缺少 'interval' 键或其值为空")

    try:
        # 验证时间间隔格式
        if not isinstance(interval_str, str):
            raise TypeError(f"interval 必须是字符串类型，当前类型: {type(interval_str)}")

        # 尝试转换
        config['interval_timedelta'] = pd.Timedelta(interval_str)

        # 验证转换结果的合理性
        timedelta_obj = config['interval_timedelta']
        if timedelta_obj <= pd.Timedelta(0):
            raise ValueError("时间间隔必须为正值")

        # 检查是否为常见的时间间隔
        total_seconds = timedelta_obj.total_seconds()
        common_intervals = [60, 300, 900, 1800, 3600, 14400, 86400]  # 1m, 5m, 15m, 30m, 1h, 4h, 1d
        if total_seconds not in common_intervals:
            print(f"警告: 目标 '{target_name}' 的时间间隔 '{interval_str}' 不是常见的交易时间框架")

    except (ValueError, TypeError) as e:
        # 具体的异常处理
        print(f"错误: 解析目标 '{target_name}' 的 interval 字符串 '{interval_str}' 失败: {e}")
        print(f"将使用默认的15分钟作为 interval_timedelta")
        config['interval_timedelta'] = pd.Timedelta(minutes=15)
    except Exception as e:
        # 其他未预期的异常
        print(f"未预期的错误: 处理目标 '{target_name}' 的时间间隔时发生异常: {e}")
        print(f"将使用默认的15分钟作为 interval_timedelta")
        config['interval_timedelta'] = pd.Timedelta(minutes=15)


def _validate_runtime_types(config: ConfigDict, target_name: str) -> None:
    """
    运行时类型验证，确保配置字典符合TargetConfig类型定义

    Args:
        config: 配置字典
        target_name: 目标名称，用于错误信息

    Raises:
        TypeError: 如果配置项类型不匹配
        ValueError: 如果配置项值无效
    """
    # 运行时类型检查
    if not isinstance(config, dict):
        raise TypeError(f"config 必须是字典类型，当前类型: {type(config)}")
    if not isinstance(target_name, str):
        raise TypeError(f"target_name 必须是字符串类型，当前类型: {type(target_name)}")

    # 定义类型验证规则
    type_validators = {
        # 基本信息类型验证
        'name': (str, "名称必须是字符串"),
        'symbol': (str, "交易对必须是字符串"),
        'device_type': (str, "设备类型必须是字符串"),
        'interval': (str, "时间间隔必须是字符串"),
        'prediction_periods': (list, "预测周期必须是列表"),
        'target_variable_type': (str, "目标变量类型必须是字符串"),
        'drop_neutral_targets': (bool, "drop_neutral_targets必须是布尔值"),
        'prediction_trigger_type': (str, "触发类型必须是字符串"),

        # 数值类型验证
        'target_threshold': ((int, float), "目标阈值必须是数值"),
        'signal_threshold': ((int, float), "信号阈值必须是数值"),
        'data_fetch_limit': (int, "数据获取限制必须是整数"),
        'train_ratio': ((int, float), "训练比例必须是数值"),
        'validation_ratio': ((int, float), "验证比例必须是数值"),

        # 布尔类型验证
        'enable_price_change': (bool, "价格变化特征开关必须是布尔值"),
        'enable_volume': (bool, "成交量特征开关必须是布尔值"),
        'enable_candle': (bool, "K线特征开关必须是布尔值"),
        'enable_ta': (bool, "技术指标特征开关必须是布尔值"),
        'enable_time': (bool, "时间特征开关必须是布尔值"),
        'enable_fund_flow': (bool, "资金流特征开关必须是布尔值"),
        'enable_mtfa': (bool, "多时间框架特征开关必须是布尔值"),
        'enable_pattern_recognition': (bool, "模式识别特征开关必须是布尔值"),
        'enable_trend_slope': (bool, "趋势斜率特征开关必须是布尔值"),

        # LightGBM参数类型验证
        'objective': (str, "目标函数必须是字符串"),
        'metric': (str, "评估指标必须是字符串"),
        'boosting_type': (str, "提升类型必须是字符串"),
        'random_state': (int, "随机种子必须是整数"),
        'n_estimators': (int, "估计器数量必须是整数"),
        'early_stopping_rounds': (int, "早停轮数必须是整数"),
        'learning_rate': ((int, float), "学习率必须是数值"),
        'num_leaves': (int, "叶子数量必须是整数"),
        'max_depth': (int, "最大深度必须是整数"),
        'reg_alpha': ((int, float), "L1正则化必须是数值"),
        'reg_lambda': ((int, float), "L2正则化必须是数值"),
        'colsample_bytree': ((int, float), "列采样比例必须是数值"),
        'subsample': ((int, float), "行采样比例必须是数值"),
        'min_child_samples': (int, "最小子样本数必须是整数"),

        # SMOTE参数类型验证
        'smote_enable': (bool, "SMOTE开关必须是布尔值"),
        'smote_k_neighbors': (int, "SMOTE邻居数必须是整数"),
        'smote_min_samples_threshold': (int, "SMOTE最小样本阈值必须是整数"),
        'smote_random_state': (int, "SMOTE随机种子必须是整数"),

        # 阈值优化参数类型验证
        'threshold_optimization_enable': (bool, "阈值优化开关必须是布尔值"),
        'threshold_optimization_method': (str, "阈值优化方法必须是字符串"),
        'threshold_save_to_metadata': (bool, "阈值保存开关必须是布尔值"),
        'threshold_default_value': ((int, float), "默认阈值必须是数值"),
        'threshold_use_independent_validation': (bool, "独立验证开关必须是布尔值"),
        'threshold_independent_val_ratio': ((int, float), "独立验证比例必须是数值"),
    }

    # 执行类型验证
    for key, value in config.items():
        if key in type_validators:
            expected_type, error_msg = type_validators[key]
            if not isinstance(value, expected_type):
                raise TypeError(f"目标 '{target_name}' 的配置项 '{key}': {error_msg}。"
                              f"期望类型: {expected_type}，实际类型: {type(value)}，实际值: {value}")

    # 特殊值验证
    _validate_special_values(config, target_name)


def _validate_special_values(config: ConfigDict, target_name: str) -> None:
    """
    验证特殊值的有效性（如枚举值、范围等）

    Args:
        config: 配置字典
        target_name: 目标名称，用于错误信息

    Raises:
        ValueError: 如果配置值无效
    """
    # 设备类型验证
    if 'device_type' in config:
        valid_devices = [d.value for d in DeviceType]
        if config['device_type'] not in valid_devices:
            raise ValueError(f"目标 '{target_name}' 的 device_type '{config['device_type']}' 无效。"
                           f"有效值: {valid_devices}")

    # 目标变量类型验证
    if 'target_variable_type' in config:
        valid_types = [t.value for t in TargetType]
        if config['target_variable_type'] not in valid_types:
            raise ValueError(f"目标 '{target_name}' 的 target_variable_type '{config['target_variable_type']}' 无效。"
                           f"有效值: {valid_types}")

    # 触发类型验证
    if 'prediction_trigger_type' in config:
        valid_triggers = [t.value for t in TriggerType]
        if config['prediction_trigger_type'] not in valid_triggers:
            raise ValueError(f"目标 '{target_name}' 的 prediction_trigger_type '{config['prediction_trigger_type']}' 无效。"
                           f"有效值: {valid_triggers}")

    # 缩放器类型验证
    if 'scaler_type' in config:
        valid_scalers = ['minmax', 'standard']
        if config['scaler_type'] not in valid_scalers:
            raise ValueError(f"目标 '{target_name}' 的 scaler_type '{config['scaler_type']}' 无效。"
                           f"有效值: {valid_scalers}")

    # 数值范围验证
    numeric_ranges = {
        'target_threshold': (0.0, 1.0),
        'signal_threshold': (0.0, 1.0),
        'train_ratio': (0.1, 0.9),
        'validation_ratio': (0.05, 0.5),
        'learning_rate': (0.001, 1.0),
        'colsample_bytree': (0.1, 1.0),
        'subsample': (0.1, 1.0),
        'threshold_default_value': (0.0, 1.0),
        'threshold_independent_val_ratio': (0.05, 0.5),
    }

    for param, (min_val, max_val) in numeric_ranges.items():
        if param in config:
            value = config[param]
            if isinstance(value, (int, float)) and not (min_val <= value <= max_val):
                raise ValueError(f"目标 '{target_name}' 的 {param} 值 {value} 超出有效范围 [{min_val}, {max_val}]")

    # 列表类型内容验证
    if 'prediction_periods' in config:
        periods = config['prediction_periods']
        if not all(isinstance(p, int) and p >= 0 for p in periods):  # 允许0值，用于元模型显示
            raise ValueError(f"目标 '{target_name}' 的 prediction_periods 必须是非负整数列表，当前值: {periods}")

    # 字典类型验证
    if 'class_weight' in config and config['class_weight'] is not None:
        class_weight = config['class_weight']
        if not isinstance(class_weight, dict):
            raise ValueError(f"目标 '{target_name}' 的 class_weight 必须是字典或None，当前类型: {type(class_weight)}")
        if not all(isinstance(k, int) and isinstance(v, (int, float)) for k, v in class_weight.items()):
            raise ValueError(f"目标 '{target_name}' 的 class_weight 字典的键必须是整数，值必须是数值")


# --- 用于快速访问所有已定义的目标名称的列表 ---
ALL_TARGET_NAMES = [t['name'] for t in PREDICTION_TARGETS if isinstance(t, dict) and 'name' in t]
